nohup: ignoring input
INFO:     Started server process [68836]
INFO:     Waiting for application startup.
2025-08-03 17:06:00,477 - app.main - INFO - Initializing Entity Extraction API
2025-08-03 17:06:00,935 INFO sqlalchemy.engine.Engine SELECT DATABASE()
2025-08-03 17:06:00,935 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-03 17:06:00,936 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 17:06:00,936 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 17:06:01,056 INFO sqlalchemy.engine.Engine SELECT @@sql_mode
2025-08-03 17:06:01,056 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-03 17:06:01,056 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 17:06:01,056 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 17:06:01,126 INFO sqlalchemy.engine.Engine SELECT @@lower_case_table_names
2025-08-03 17:06:01,126 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-03 17:06:01,126 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 17:06:01,126 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 17:06:01,247 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:06:01,247 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:06:01,247 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`website_urls_gemini`
2025-08-03 17:06:01,247 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`website_urls_gemini`
2025-08-03 17:06:01,247 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 17:06:01,247 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 17:06:01,325 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`general_logs_gemini`
2025-08-03 17:06:01,325 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`general_logs_gemini`
2025-08-03 17:06:01,325 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 17:06:01,325 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 17:06:01,397 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`mcc_url_classification_gemini`
2025-08-03 17:06:01,397 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`mcc_url_classification_gemini`
2025-08-03 17:06:01,397 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 17:06:01,397 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 17:06:01,456 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`policy_url_classification_gemini`
2025-08-03 17:06:01,456 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`policy_url_classification_gemini`
2025-08-03 17:06:01,456 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 17:06:01,456 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 17:06:01,516 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`policy_analysis_new_gemini`
2025-08-03 17:06:01,516 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`policy_analysis_new_gemini`
2025-08-03 17:06:01,517 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 17:06:01,517 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 17:06:01,584 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`entity_extraction_analysis`
2025-08-03 17:06:01,584 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`entity_extraction_analysis`
2025-08-03 17:06:01,584 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 17:06:01,584 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 17:06:01,666 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`entity_extraction_url_analysis`
2025-08-03 17:06:01,666 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`entity_extraction_url_analysis`
2025-08-03 17:06:01,666 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 17:06:01,666 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 17:06:01,741 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 17:06:01,741 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 17:06:01,865 - app.main - INFO - Database initialized successfully
Creating Entity Extractor database tables...
2025-08-03 17:06:01,925 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:06:01,925 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:06:01,925 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`website_urls_gemini`
2025-08-03 17:06:01,925 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`website_urls_gemini`
2025-08-03 17:06:01,925 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 17:06:01,925 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 17:06:01,987 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`general_logs_gemini`
2025-08-03 17:06:01,987 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`general_logs_gemini`
2025-08-03 17:06:01,987 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 17:06:01,987 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 17:06:02,067 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`mcc_url_classification_gemini`
2025-08-03 17:06:02,067 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`mcc_url_classification_gemini`
2025-08-03 17:06:02,067 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 17:06:02,067 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 17:06:02,126 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`policy_url_classification_gemini`
2025-08-03 17:06:02,126 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`policy_url_classification_gemini`
2025-08-03 17:06:02,126 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 17:06:02,126 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 17:06:02,188 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`policy_analysis_new_gemini`
2025-08-03 17:06:02,188 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`policy_analysis_new_gemini`
2025-08-03 17:06:02,188 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 17:06:02,188 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 17:06:02,255 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`entity_extraction_analysis`
2025-08-03 17:06:02,255 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`entity_extraction_analysis`
2025-08-03 17:06:02,255 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 17:06:02,255 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 17:06:02,334 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`entity_extraction_url_analysis`
2025-08-03 17:06:02,334 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`entity_extraction_url_analysis`
2025-08-03 17:06:02,334 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 17:06:02,334 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 17:06:02,388 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 17:06:02,388 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 17:06:02,516 - app.main - INFO - Entity Extractor tables initialized successfully
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
✅ Entity Extractor database tables created successfully!
Tables created:
  - entity_extraction_analysis
  - entity_extraction_url_analysis
[2025-08-03 17:06:09][EntityExtractor][orchestrator_************************************][************************************] INFO: Starting simplified entity extraction orchestration
{
  "scrape_request_ref_id": "************************************",
  "website_url": "https://www.shell.in",
  "org_id": "2"
}
2025-08-03 17:06:10,076 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:06:10,076 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:06:10,077 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:10,077 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:10,077 INFO sqlalchemy.engine.Engine [generated in 0.00022s] {'analysis_id': 0, 'timestamp': '2025-08-03T17:06:09.998987', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Starting simplified entity extraction orchestration", "scrape_request_ref_id": "************************************",  ... (22 characters truncated) ... 03T17:06:09.998978", "data": {"scrape_request_ref_id": "************************************", "website_url": "https://www.shell.in", "org_id": "2"}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:10,077 - sqlalchemy.engine.Engine - INFO - [generated in 0.00022s] {'analysis_id': 0, 'timestamp': '2025-08-03T17:06:09.998987', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Starting simplified entity extraction orchestration", "scrape_request_ref_id": "************************************",  ... (22 characters truncated) ... 03T17:06:09.998978", "data": {"scrape_request_ref_id": "************************************", "website_url": "https://www.shell.in", "org_id": "2"}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:10,146 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 17:06:10,146 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 17:06:10,345 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:06:10,345 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:06:10,348 INFO sqlalchemy.engine.Engine SELECT entity_extraction_analysis.id, entity_extraction_analysis.scrape_request_ref_id, entity_extraction_analysis.website_url, entity_extraction_analysis.processing_status, entity_extraction_analysis.legal_name, entity_extraction_analysis.business_email, entity_extraction_analysis.support_email, entity_extraction_analysis.business_contact_numbers, entity_extraction_analysis.business_location, entity_extraction_analysis.has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details, entity_extraction_analysis.accepts_international_orders, entity_extraction_analysis.shipping_policy_details, entity_extraction_analysis.jurisdiction_place, entity_extraction_analysis.shipping_countries, entity_extraction_analysis.privacy_policy_text, entity_extraction_analysis.terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method, entity_extraction_analysis.total_urls_processed, entity_extraction_analysis.all_urls_found, entity_extraction_analysis.reachable_urls, entity_extraction_analysis.unreachable_urls, entity_extraction_analysis.policy_urls_matched, entity_extraction_analysis.created_at, entity_extraction_analysis.started_at, entity_extraction_analysis.completed_at, entity_extraction_analysis.error_message, entity_extraction_analysis.org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.scrape_request_ref_id = %(scrape_request_ref_id_1)s AND entity_extraction_analysis.org_id = %(org_id_1)s ORDER BY entity_extraction_analysis.id DESC
2025-08-03 17:06:10,348 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_analysis.id, entity_extraction_analysis.scrape_request_ref_id, entity_extraction_analysis.website_url, entity_extraction_analysis.processing_status, entity_extraction_analysis.legal_name, entity_extraction_analysis.business_email, entity_extraction_analysis.support_email, entity_extraction_analysis.business_contact_numbers, entity_extraction_analysis.business_location, entity_extraction_analysis.has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details, entity_extraction_analysis.accepts_international_orders, entity_extraction_analysis.shipping_policy_details, entity_extraction_analysis.jurisdiction_place, entity_extraction_analysis.shipping_countries, entity_extraction_analysis.privacy_policy_text, entity_extraction_analysis.terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method, entity_extraction_analysis.total_urls_processed, entity_extraction_analysis.all_urls_found, entity_extraction_analysis.reachable_urls, entity_extraction_analysis.unreachable_urls, entity_extraction_analysis.policy_urls_matched, entity_extraction_analysis.created_at, entity_extraction_analysis.started_at, entity_extraction_analysis.completed_at, entity_extraction_analysis.error_message, entity_extraction_analysis.org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.scrape_request_ref_id = %(scrape_request_ref_id_1)s AND entity_extraction_analysis.org_id = %(org_id_1)s ORDER BY entity_extraction_analysis.id DESC
2025-08-03 17:06:10,349 INFO sqlalchemy.engine.Engine [generated in 0.00020s] {'scrape_request_ref_id_1': '************************************', 'org_id_1': '2'}
2025-08-03 17:06:10,349 - sqlalchemy.engine.Engine - INFO - [generated in 0.00020s] {'scrape_request_ref_id_1': '************************************', 'org_id_1': '2'}
[2025-08-03 17:06:10][EntityExtractor][orchestrator_************************************][************************************] INFO: Found existing analysis with ID: 168, status: COMPLETED
2025-08-03 17:06:10,743 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:06:10,743 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:06:10,743 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:10,743 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:10,743 INFO sqlalchemy.engine.Engine [cached since 0.6661s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T17:06:10.415679', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found existing analysis with ID: 168, status: COMPLETED", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:10.415670", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:10,743 - sqlalchemy.engine.Engine - INFO - [cached since 0.6661s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T17:06:10.415679', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found existing analysis with ID: 168, status: COMPLETED", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:10.415670", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:10,810 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 17:06:10,810 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 17:06:10,920 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-03 17:06:10,920 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-03 17:06:11,100 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:06:11,100 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:06:11,101 INFO sqlalchemy.engine.Engine SELECT entity_extraction_analysis.id, entity_extraction_analysis.scrape_request_ref_id, entity_extraction_analysis.website_url, entity_extraction_analysis.processing_status, entity_extraction_analysis.legal_name, entity_extraction_analysis.business_email, entity_extraction_analysis.support_email, entity_extraction_analysis.business_contact_numbers, entity_extraction_analysis.business_location, entity_extraction_analysis.has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details, entity_extraction_analysis.accepts_international_orders, entity_extraction_analysis.shipping_policy_details, entity_extraction_analysis.jurisdiction_place, entity_extraction_analysis.shipping_countries, entity_extraction_analysis.privacy_policy_text, entity_extraction_analysis.terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method, entity_extraction_analysis.total_urls_processed, entity_extraction_analysis.all_urls_found, entity_extraction_analysis.reachable_urls, entity_extraction_analysis.unreachable_urls, entity_extraction_analysis.policy_urls_matched, entity_extraction_analysis.created_at, entity_extraction_analysis.started_at, entity_extraction_analysis.completed_at, entity_extraction_analysis.error_message, entity_extraction_analysis.org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.scrape_request_ref_id = %(scrape_request_ref_id_1)s AND entity_extraction_analysis.org_id = %(org_id_1)s ORDER BY entity_extraction_analysis.id DESC
2025-08-03 17:06:11,101 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_analysis.id, entity_extraction_analysis.scrape_request_ref_id, entity_extraction_analysis.website_url, entity_extraction_analysis.processing_status, entity_extraction_analysis.legal_name, entity_extraction_analysis.business_email, entity_extraction_analysis.support_email, entity_extraction_analysis.business_contact_numbers, entity_extraction_analysis.business_location, entity_extraction_analysis.has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details, entity_extraction_analysis.accepts_international_orders, entity_extraction_analysis.shipping_policy_details, entity_extraction_analysis.jurisdiction_place, entity_extraction_analysis.shipping_countries, entity_extraction_analysis.privacy_policy_text, entity_extraction_analysis.terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method, entity_extraction_analysis.total_urls_processed, entity_extraction_analysis.all_urls_found, entity_extraction_analysis.reachable_urls, entity_extraction_analysis.unreachable_urls, entity_extraction_analysis.policy_urls_matched, entity_extraction_analysis.created_at, entity_extraction_analysis.started_at, entity_extraction_analysis.completed_at, entity_extraction_analysis.error_message, entity_extraction_analysis.org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.scrape_request_ref_id = %(scrape_request_ref_id_1)s AND entity_extraction_analysis.org_id = %(org_id_1)s ORDER BY entity_extraction_analysis.id DESC
2025-08-03 17:06:11,101 INFO sqlalchemy.engine.Engine [cached since 0.7522s ago] {'scrape_request_ref_id_1': '************************************', 'org_id_1': '2'}
2025-08-03 17:06:11,101 - sqlalchemy.engine.Engine - INFO - [cached since 0.7522s ago] {'scrape_request_ref_id_1': '************************************', 'org_id_1': '2'}
[2025-08-03 17:06:11][EntityExtractor][orchestrator_************************************][************************************] WARNING: Found existing analysis during create - returning existing ID: 168
2025-08-03 17:06:11,246 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:06:11,246 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:06:11,247 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:11,247 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:11,247 INFO sqlalchemy.engine.Engine [cached since 1.17s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T17:06:11.186821', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "Found existing analysis during create - returning existing ID: 168", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:11.186814", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:11,247 - sqlalchemy.engine.Engine - INFO - [cached since 1.17s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T17:06:11.186821', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "Found existing analysis during create - returning existing ID: 168", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:11.186814", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:11,316 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 17:06:11,316 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 17:06:11,465 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-03 17:06:11,465 - sqlalchemy.engine.Engine - INFO - ROLLBACK
[2025-08-03 17:06:11][EntityExtractor][168][************************************] INFO: Updated logger with analysis ID: 168
2025-08-03 17:06:11,666 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:06:11,666 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:06:11,666 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:11,666 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:11,666 INFO sqlalchemy.engine.Engine [cached since 1.589s ago] {'analysis_id': 168, 'timestamp': '2025-08-03T17:06:11.575241', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Updated logger with analysis ID: 168", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:11.575233", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:11,666 - sqlalchemy.engine.Engine - INFO - [cached since 1.589s ago] {'analysis_id': 168, 'timestamp': '2025-08-03T17:06:11.575241', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Updated logger with analysis ID: 168", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:11.575233", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:11,730 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 17:06:11,730 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 17:06:11,907 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:06:11,907 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:06:11,911 INFO sqlalchemy.engine.Engine SELECT entity_extraction_analysis.id AS entity_extraction_analysis_id, entity_extraction_analysis.scrape_request_ref_id AS entity_extraction_analysis_scrape_request_ref_id, entity_extraction_analysis.website_url AS entity_extraction_analysis_website_url, entity_extraction_analysis.processing_status AS entity_extraction_analysis_processing_status, entity_extraction_analysis.legal_name AS entity_extraction_analysis_legal_name, entity_extraction_analysis.business_email AS entity_extraction_analysis_business_email, entity_extraction_analysis.support_email AS entity_extraction_analysis_support_email, entity_extraction_analysis.business_contact_numbers AS entity_extraction_analysis_business_contact_numbers, entity_extraction_analysis.business_location AS entity_extraction_analysis_business_location, entity_extraction_analysis.has_jurisdiction_law AS entity_extraction_analysis_has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details AS entity_extraction_analysis_jurisdiction_details, entity_extraction_analysis.accepts_international_orders AS entity_extraction_analysis_accepts_international_orders, entity_extraction_analysis.shipping_policy_details AS entity_extraction_analysis_shipping_policy_details, entity_extraction_analysis.jurisdiction_place AS entity_extraction_analysis_jurisdiction_place, entity_extraction_analysis.shipping_countries AS entity_extraction_analysis_shipping_countries, entity_extraction_analysis.privacy_policy_text AS entity_extraction_analysis_privacy_policy_text, entity_extraction_analysis.terms_conditions_text AS entity_extraction_analysis_terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini AS entity_extraction_analysis_urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini AS entity_extraction_analysis_urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method AS entity_extraction_analysis_extraction_method, entity_extraction_analysis.total_urls_processed AS entity_extraction_analysis_total_urls_processed, entity_extraction_analysis.all_urls_found AS entity_extraction_analysis_all_urls_found, entity_extraction_analysis.reachable_urls AS entity_extraction_analysis_reachable_urls, entity_extraction_analysis.unreachable_urls AS entity_extraction_analysis_unreachable_urls, entity_extraction_analysis.policy_urls_matched AS entity_extraction_analysis_policy_urls_matched, entity_extraction_analysis.created_at AS entity_extraction_analysis_created_at, entity_extraction_analysis.started_at AS entity_extraction_analysis_started_at, entity_extraction_analysis.completed_at AS entity_extraction_analysis_completed_at, entity_extraction_analysis.error_message AS entity_extraction_analysis_error_message, entity_extraction_analysis.org_id AS entity_extraction_analysis_org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.id = %(pk_1)s
2025-08-03 17:06:11,911 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_analysis.id AS entity_extraction_analysis_id, entity_extraction_analysis.scrape_request_ref_id AS entity_extraction_analysis_scrape_request_ref_id, entity_extraction_analysis.website_url AS entity_extraction_analysis_website_url, entity_extraction_analysis.processing_status AS entity_extraction_analysis_processing_status, entity_extraction_analysis.legal_name AS entity_extraction_analysis_legal_name, entity_extraction_analysis.business_email AS entity_extraction_analysis_business_email, entity_extraction_analysis.support_email AS entity_extraction_analysis_support_email, entity_extraction_analysis.business_contact_numbers AS entity_extraction_analysis_business_contact_numbers, entity_extraction_analysis.business_location AS entity_extraction_analysis_business_location, entity_extraction_analysis.has_jurisdiction_law AS entity_extraction_analysis_has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details AS entity_extraction_analysis_jurisdiction_details, entity_extraction_analysis.accepts_international_orders AS entity_extraction_analysis_accepts_international_orders, entity_extraction_analysis.shipping_policy_details AS entity_extraction_analysis_shipping_policy_details, entity_extraction_analysis.jurisdiction_place AS entity_extraction_analysis_jurisdiction_place, entity_extraction_analysis.shipping_countries AS entity_extraction_analysis_shipping_countries, entity_extraction_analysis.privacy_policy_text AS entity_extraction_analysis_privacy_policy_text, entity_extraction_analysis.terms_conditions_text AS entity_extraction_analysis_terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini AS entity_extraction_analysis_urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini AS entity_extraction_analysis_urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method AS entity_extraction_analysis_extraction_method, entity_extraction_analysis.total_urls_processed AS entity_extraction_analysis_total_urls_processed, entity_extraction_analysis.all_urls_found AS entity_extraction_analysis_all_urls_found, entity_extraction_analysis.reachable_urls AS entity_extraction_analysis_reachable_urls, entity_extraction_analysis.unreachable_urls AS entity_extraction_analysis_unreachable_urls, entity_extraction_analysis.policy_urls_matched AS entity_extraction_analysis_policy_urls_matched, entity_extraction_analysis.created_at AS entity_extraction_analysis_created_at, entity_extraction_analysis.started_at AS entity_extraction_analysis_started_at, entity_extraction_analysis.completed_at AS entity_extraction_analysis_completed_at, entity_extraction_analysis.error_message AS entity_extraction_analysis_error_message, entity_extraction_analysis.org_id AS entity_extraction_analysis_org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.id = %(pk_1)s
2025-08-03 17:06:11,911 INFO sqlalchemy.engine.Engine [generated in 0.00017s] {'pk_1': 168}
2025-08-03 17:06:11,911 - sqlalchemy.engine.Engine - INFO - [generated in 0.00017s] {'pk_1': 168}
2025-08-03 17:06:12,000 INFO sqlalchemy.engine.Engine UPDATE entity_extraction_analysis SET processing_status=%(processing_status)s, started_at=%(started_at)s WHERE entity_extraction_analysis.id = %(entity_extraction_analysis_id)s
2025-08-03 17:06:12,000 - sqlalchemy.engine.Engine - INFO - UPDATE entity_extraction_analysis SET processing_status=%(processing_status)s, started_at=%(started_at)s WHERE entity_extraction_analysis.id = %(entity_extraction_analysis_id)s
2025-08-03 17:06:12,000 INFO sqlalchemy.engine.Engine [generated in 0.00018s] {'processing_status': 'IN_PROGRESS', 'started_at': '2025-08-03T17:06:11.845741', 'entity_extraction_analysis_id': 168}
2025-08-03 17:06:12,000 - sqlalchemy.engine.Engine - INFO - [generated in 0.00018s] {'processing_status': 'IN_PROGRESS', 'started_at': '2025-08-03T17:06:11.845741', 'entity_extraction_analysis_id': 168}
2025-08-03 17:06:12,073 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 17:06:12,073 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 17:06:12][EntityExtractor][168][************************************] INFO: Updated analysis 168 status to IN_PROGRESS
2025-08-03 17:06:12,266 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:06:12,266 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:06:12,266 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:12,266 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:12,267 INFO sqlalchemy.engine.Engine [cached since 2.189s ago] {'analysis_id': 168, 'timestamp': '2025-08-03T17:06:12.181084', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Updated analysis 168 status to IN_PROGRESS", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:12.181075", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:12,267 - sqlalchemy.engine.Engine - INFO - [cached since 2.189s ago] {'analysis_id': 168, 'timestamp': '2025-08-03T17:06:12.181084', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Updated analysis 168 status to IN_PROGRESS", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:12.181075", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:12,335 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 17:06:12,335 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 17:06:12][EntityExtractor][url_retrieval_************************************][************************************] INFO: Retrieving policy URLs with reachability status
2025-08-03 17:06:12,530 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:06:12,530 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:06:12,531 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:12,531 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:12,531 INFO sqlalchemy.engine.Engine [cached since 2.454s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T17:06:12.471167', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Retrieving policy URLs with reachability status", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:12.471160", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:12,531 - sqlalchemy.engine.Engine - INFO - [cached since 2.454s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T17:06:12.471167', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Retrieving policy URLs with reachability status", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:12.471160", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:12,590 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 17:06:12,590 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 17:06:13][EntityExtractor][url_retrieval_************************************][************************************] INFO: Found latest scrape_request_ref_id: ************************************ for domain: shell.in
2025-08-03 17:06:14,015 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:06:14,015 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:06:14,016 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:14,016 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:14,016 INFO sqlalchemy.engine.Engine [cached since 3.938s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T17:06:13.949350', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found latest scrape_request_ref_id: ************************************ for domain: shell.in", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:13.949341", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:14,016 - sqlalchemy.engine.Engine - INFO - [cached since 3.938s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T17:06:13.949350', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found latest scrape_request_ref_id: ************************************ for domain: shell.in", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:13.949341", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:14,075 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 17:06:14,075 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 17:06:14][EntityExtractor][url_retrieval_************************************][************************************] INFO: MCC only provided 0 policy types, force-loading 5 missing policies from policy_analysis_new_gemini
2025-08-03 17:06:14,585 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:06:14,585 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:06:14,586 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:14,586 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:14,586 INFO sqlalchemy.engine.Engine [cached since 4.508s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T17:06:14.528556', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "MCC only provided 0 policy types, force-loading 5 missing policies from policy_analysis_new_gemini", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:14.528549", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:14,586 - sqlalchemy.engine.Engine - INFO - [cached since 4.508s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T17:06:14.528556', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "MCC only provided 0 policy types, force-loading 5 missing policies from policy_analysis_new_gemini", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:14.528549", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:14,645 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 17:06:14,645 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 17:06:15][EntityExtractor][url_retrieval_************************************][************************************] INFO: Found latest scrape_request_ref_id: ************************************ for domain: shell.in
2025-08-03 17:06:15,390 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:06:15,390 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:06:15,391 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:15,391 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:15,391 INFO sqlalchemy.engine.Engine [cached since 5.313s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T17:06:15.229412', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found latest scrape_request_ref_id: ************************************ for domain: shell.in", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:15.229404", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:15,391 - sqlalchemy.engine.Engine - INFO - [cached since 5.313s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T17:06:15.229412', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found latest scrape_request_ref_id: ************************************ for domain: shell.in", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:15.229404", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:15,528 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 17:06:15,528 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 17:06:16][EntityExtractor][url_retrieval_************************************][************************************] INFO: Retrieved policy analysis data for 6 policy types
2025-08-03 17:06:16,195 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:06:16,195 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:06:16,195 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:16,195 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:16,195 INFO sqlalchemy.engine.Engine [cached since 6.118s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T17:06:16.150529', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Retrieved policy analysis data for 6 policy types", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:16.150522", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:16,195 - sqlalchemy.engine.Engine - INFO - [cached since 6.118s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T17:06:16.150529', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Retrieved policy analysis data for 6 policy types", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:16.150522", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:16,285 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 17:06:16,285 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 17:06:16][EntityExtractor][url_retrieval_************************************][************************************] INFO: Force-loaded 5 policies from policy_analysis_new_gemini as unreachable
2025-08-03 17:06:16,715 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:06:16,715 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:06:16,715 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:16,715 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:16,715 INFO sqlalchemy.engine.Engine [cached since 6.638s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T17:06:16.653536', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Force-loaded 5 policies from policy_analysis_new_gemini as unreachable", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:16.653528", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:16,715 - sqlalchemy.engine.Engine - INFO - [cached since 6.638s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T17:06:16.653536', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Force-loaded 5 policies from policy_analysis_new_gemini as unreachable", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:16.653528", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:16,793 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 17:06:16,793 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 17:06:16][EntityExtractor][url_retrieval_************************************][************************************] INFO: Total policy URLs retrieved: 5
2025-08-03 17:06:16,971 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:06:16,971 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:06:16,971 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:16,971 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:16,971 INFO sqlalchemy.engine.Engine [cached since 6.894s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T17:06:16.905735', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Total policy URLs retrieved: 5", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:16.905728", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:16,971 - sqlalchemy.engine.Engine - INFO - [cached since 6.894s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T17:06:16.905735', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Total policy URLs retrieved: 5", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:16.905728", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:17,037 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 17:06:17,037 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 17:06:17][EntityExtractor][url_retrieval_************************************][************************************] INFO: Found latest scrape_request_ref_id: ************************************ for domain: shell.in
2025-08-03 17:06:17,733 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:06:17,733 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:06:17,733 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:17,733 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:17,733 INFO sqlalchemy.engine.Engine [cached since 7.656s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T17:06:17.684331', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found latest scrape_request_ref_id: ************************************ for domain: shell.in", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:17.684323", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:17,733 - sqlalchemy.engine.Engine - INFO - [cached since 7.656s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T17:06:17.684331', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found latest scrape_request_ref_id: ************************************ for domain: shell.in", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:17.684323", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:17,839 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 17:06:17,839 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 17:06:18][EntityExtractor][url_retrieval_************************************][************************************] INFO: Retrieved policy analysis data for 6 policy types
2025-08-03 17:06:18,400 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:06:18,400 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:06:18,400 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:18,400 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:18,401 INFO sqlalchemy.engine.Engine [cached since 8.323s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T17:06:18.343054', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Retrieved policy analysis data for 6 policy types", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:18.343047", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:18,401 - sqlalchemy.engine.Engine - INFO - [cached since 8.323s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T17:06:18.343054', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Retrieved policy analysis data for 6 policy types", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:18.343047", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:18,461 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 17:06:18,461 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 17:06:18][EntityExtractor][168][************************************] INFO: Filtered policy URLs keys: ['privacy_policy', 'terms_and_condition', 'shipping_delivery', 'contact_us', 'about_us']
2025-08-03 17:06:18,945 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:06:18,945 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:06:18,946 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:18,946 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:18,946 INFO sqlalchemy.engine.Engine [cached since 8.869s ago] {'analysis_id': 168, 'timestamp': '2025-08-03T17:06:18.863001', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Filtered policy URLs keys: [\'privacy_policy\', \'terms_and_condition\', \'shipping_delivery\', \'contact_us\', \'about_us\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:18.862993", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:18,946 - sqlalchemy.engine.Engine - INFO - [cached since 8.869s ago] {'analysis_id': 168, 'timestamp': '2025-08-03T17:06:18.863001', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Filtered policy URLs keys: [\'privacy_policy\', \'terms_and_condition\', \'shipping_delivery\', \'contact_us\', \'about_us\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:18.862993", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:19,015 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 17:06:19,015 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 17:06:19][EntityExtractor][168][************************************] INFO: Extracted text length for privacy_policy: 2340
2025-08-03 17:06:19,342 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:06:19,342 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:06:19,343 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:19,343 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:19,343 INFO sqlalchemy.engine.Engine [cached since 9.266s ago] {'analysis_id': 168, 'timestamp': '2025-08-03T17:06:19.254115', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Extracted text length for privacy_policy: 2340", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:19.254108", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:19,343 - sqlalchemy.engine.Engine - INFO - [cached since 9.266s ago] {'analysis_id': 168, 'timestamp': '2025-08-03T17:06:19.254115', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Extracted text length for privacy_policy: 2340", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:19.254108", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:19,411 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 17:06:19,411 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 17:06:19][EntityExtractor][168][************************************] INFO: Extracted text length for terms_and_condition: 1479
2025-08-03 17:06:19,584 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:06:19,584 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:06:19,585 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:19,585 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:19,585 INFO sqlalchemy.engine.Engine [cached since 9.508s ago] {'analysis_id': 168, 'timestamp': '2025-08-03T17:06:19.530271', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Extracted text length for terms_and_condition: 1479", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:19.530263", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:19,585 - sqlalchemy.engine.Engine - INFO - [cached since 9.508s ago] {'analysis_id': 168, 'timestamp': '2025-08-03T17:06:19.530271', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Extracted text length for terms_and_condition: 1479", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:19.530263", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:19,657 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 17:06:19,657 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 17:06:19][EntityExtractor][url_retrieval_************************************][************************************] INFO: Checking Gemini reachability for 5 URLs
2025-08-03 17:06:19,830 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:06:19,830 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:06:19,830 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:19,830 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:19,830 INFO sqlalchemy.engine.Engine [cached since 9.753s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T17:06:19.775268', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Checking Gemini reachability for 5 URLs", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:19.775260", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:19,830 - sqlalchemy.engine.Engine - INFO - [cached since 9.753s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T17:06:19.775268', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Checking Gemini reachability for 5 URLs", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:19.775260", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:19,901 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 17:06:19,901 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 17:06:20][EntityExtractor][url_retrieval_************************************][************************************] INFO: Gemini reachability check completed: 5 reachable, 0 unreachable
2025-08-03 17:06:20,086 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:06:20,086 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:06:20,087 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:20,087 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:20,087 INFO sqlalchemy.engine.Engine [cached since 10.01s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T17:06:20.011997', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Gemini reachability check completed: 5 reachable, 0 unreachable", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:20.011990", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:20,087 - sqlalchemy.engine.Engine - INFO - [cached since 10.01s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T17:06:20.011997', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Gemini reachability check completed: 5 reachable, 0 unreachable", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:20.011990", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:20,155 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 17:06:20,155 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 17:06:23,302 - google_genai.models - INFO - AFC is enabled with max remote calls: 20000.
2025-08-03 17:06:45,705 - httpx - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent "HTTP/1.1 200 OK"
2025-08-03 17:06:45,709 - google_genai.models - INFO - AFC remote call 1 is done.
[2025-08-03 17:06:23][legacy_unknown][unknown] INFO: Starting Gemini API call
{
  "model": "gemini-2.5-flash",
  "timeout": 120,
  "max_retries": 3,
  "prompt_length": 5395,
  "context": {
    "task_type": "legacy"
  }
}
[2025-08-03 17:06:23][legacy_unknown][unknown] INFO: Gemini API attempt 1/3
[2025-08-03 17:06:45][legacy_unknown][unknown] INFO: Gemini API Usage: cache_tokens_details=None cached_content_token_count=None candidates_token_count=307 candidates_tokens_details=None prompt_token_count=1281 prompt_tokens_details=[ModalityTokenCount(
  modality=<MediaModality.TEXT: 'TEXT'>,
  token_count=1281
)] thoughts_token_count=2425 tool_use_prompt_token_count=None tool_use_prompt_tokens_details=None total_token_count=4013 traffic_type=None
[2025-08-03 17:06:45][legacy_unknown][unknown] INFO: Gemini API call successful
{
  "attempt": 1,
  "response_length": 621,
  "finish_reason": "STOP"
}
API response logged to: api_logs/gemini_20250803_170647_ff16ef02.json
2025-08-03 17:06:47,785 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:06:47,785 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:06:47,787 INFO sqlalchemy.engine.Engine SELECT entity_extraction_url_analysis.id, entity_extraction_url_analysis.analysis_id, entity_extraction_url_analysis.entity_analysis_id, entity_extraction_url_analysis.scrape_request_ref_id, entity_extraction_url_analysis.url, entity_extraction_url_analysis.url_type, entity_extraction_url_analysis.url_classification, entity_extraction_url_analysis.url_source_table, entity_extraction_url_analysis.is_reachable_by_gemini, entity_extraction_url_analysis.soft_classification, entity_extraction_url_analysis.hard_classification, entity_extraction_url_analysis.extracted_text, entity_extraction_url_analysis.extracted_text_length, entity_extraction_url_analysis.extracted_entities, entity_extraction_url_analysis.processing_method, entity_extraction_url_analysis.processing_status, entity_extraction_url_analysis.error_message, entity_extraction_url_analysis.created_at, entity_extraction_url_analysis.processed_at, entity_extraction_url_analysis.org_id 
FROM entity_extraction_url_analysis 
WHERE entity_extraction_url_analysis.analysis_id = %(analysis_id_1)s AND entity_extraction_url_analysis.url = %(url_1)s
2025-08-03 17:06:47,787 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_url_analysis.id, entity_extraction_url_analysis.analysis_id, entity_extraction_url_analysis.entity_analysis_id, entity_extraction_url_analysis.scrape_request_ref_id, entity_extraction_url_analysis.url, entity_extraction_url_analysis.url_type, entity_extraction_url_analysis.url_classification, entity_extraction_url_analysis.url_source_table, entity_extraction_url_analysis.is_reachable_by_gemini, entity_extraction_url_analysis.soft_classification, entity_extraction_url_analysis.hard_classification, entity_extraction_url_analysis.extracted_text, entity_extraction_url_analysis.extracted_text_length, entity_extraction_url_analysis.extracted_entities, entity_extraction_url_analysis.processing_method, entity_extraction_url_analysis.processing_status, entity_extraction_url_analysis.error_message, entity_extraction_url_analysis.created_at, entity_extraction_url_analysis.processed_at, entity_extraction_url_analysis.org_id 
FROM entity_extraction_url_analysis 
WHERE entity_extraction_url_analysis.analysis_id = %(analysis_id_1)s AND entity_extraction_url_analysis.url = %(url_1)s
2025-08-03 17:06:47,787 INFO sqlalchemy.engine.Engine [generated in 0.00020s] {'analysis_id_1': 168, 'url_1': 'https://www.shell.in/privacy.html'}
2025-08-03 17:06:47,787 - sqlalchemy.engine.Engine - INFO - [generated in 0.00020s] {'analysis_id_1': 168, 'url_1': 'https://www.shell.in/privacy.html'}
[2025-08-03 17:06:47][EntityExtractor][None][************************************] WARNING: URL analysis record not found for URL: https://www.shell.in/privacy.html
2025-08-03 17:06:48,125 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:06:48,125 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:06:48,125 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:48,125 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:48,126 INFO sqlalchemy.engine.Engine [cached since 38.05s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T17:06:47.936354', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "URL analysis record not found for URL: https://www.shell.in/privacy.html", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:47.936346", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:48,126 - sqlalchemy.engine.Engine - INFO - [cached since 38.05s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T17:06:47.936354', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "URL analysis record not found for URL: https://www.shell.in/privacy.html", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:47.936346", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:48,185 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 17:06:48,185 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 17:06:48,371 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-03 17:06:48,371 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-03 17:06:48,545 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:06:48,545 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:06:48,546 INFO sqlalchemy.engine.Engine SELECT entity_extraction_url_analysis.id, entity_extraction_url_analysis.analysis_id, entity_extraction_url_analysis.entity_analysis_id, entity_extraction_url_analysis.scrape_request_ref_id, entity_extraction_url_analysis.url, entity_extraction_url_analysis.url_type, entity_extraction_url_analysis.url_classification, entity_extraction_url_analysis.url_source_table, entity_extraction_url_analysis.is_reachable_by_gemini, entity_extraction_url_analysis.soft_classification, entity_extraction_url_analysis.hard_classification, entity_extraction_url_analysis.extracted_text, entity_extraction_url_analysis.extracted_text_length, entity_extraction_url_analysis.extracted_entities, entity_extraction_url_analysis.processing_method, entity_extraction_url_analysis.processing_status, entity_extraction_url_analysis.error_message, entity_extraction_url_analysis.created_at, entity_extraction_url_analysis.processed_at, entity_extraction_url_analysis.org_id 
FROM entity_extraction_url_analysis 
WHERE entity_extraction_url_analysis.analysis_id = %(analysis_id_1)s AND entity_extraction_url_analysis.url = %(url_1)s
2025-08-03 17:06:48,546 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_url_analysis.id, entity_extraction_url_analysis.analysis_id, entity_extraction_url_analysis.entity_analysis_id, entity_extraction_url_analysis.scrape_request_ref_id, entity_extraction_url_analysis.url, entity_extraction_url_analysis.url_type, entity_extraction_url_analysis.url_classification, entity_extraction_url_analysis.url_source_table, entity_extraction_url_analysis.is_reachable_by_gemini, entity_extraction_url_analysis.soft_classification, entity_extraction_url_analysis.hard_classification, entity_extraction_url_analysis.extracted_text, entity_extraction_url_analysis.extracted_text_length, entity_extraction_url_analysis.extracted_entities, entity_extraction_url_analysis.processing_method, entity_extraction_url_analysis.processing_status, entity_extraction_url_analysis.error_message, entity_extraction_url_analysis.created_at, entity_extraction_url_analysis.processed_at, entity_extraction_url_analysis.org_id 
FROM entity_extraction_url_analysis 
WHERE entity_extraction_url_analysis.analysis_id = %(analysis_id_1)s AND entity_extraction_url_analysis.url = %(url_1)s
2025-08-03 17:06:48,546 INFO sqlalchemy.engine.Engine [cached since 0.7588s ago] {'analysis_id_1': 168, 'url_1': 'https://www.shell.in/terms-and-conditions.html'}
2025-08-03 17:06:48,546 - sqlalchemy.engine.Engine - INFO - [cached since 0.7588s ago] {'analysis_id_1': 168, 'url_1': 'https://www.shell.in/terms-and-conditions.html'}
[2025-08-03 17:06:48][EntityExtractor][None][************************************] WARNING: URL analysis record not found for URL: https://www.shell.in/terms-and-conditions.html
2025-08-03 17:06:48,670 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:06:48,670 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:06:48,670 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:48,670 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:48,670 INFO sqlalchemy.engine.Engine [cached since 38.59s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T17:06:48.615886', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "URL analysis record not found for URL: https://www.shell.in/terms-and-conditions.html", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:48.615878", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:48,670 - sqlalchemy.engine.Engine - INFO - [cached since 38.59s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T17:06:48.615886', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "URL analysis record not found for URL: https://www.shell.in/terms-and-conditions.html", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:48.615878", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:48,740 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 17:06:48,740 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 17:06:48,850 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-03 17:06:48,850 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-03 17:06:49,020 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:06:49,020 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:06:49,021 INFO sqlalchemy.engine.Engine SELECT entity_extraction_url_analysis.id, entity_extraction_url_analysis.analysis_id, entity_extraction_url_analysis.entity_analysis_id, entity_extraction_url_analysis.scrape_request_ref_id, entity_extraction_url_analysis.url, entity_extraction_url_analysis.url_type, entity_extraction_url_analysis.url_classification, entity_extraction_url_analysis.url_source_table, entity_extraction_url_analysis.is_reachable_by_gemini, entity_extraction_url_analysis.soft_classification, entity_extraction_url_analysis.hard_classification, entity_extraction_url_analysis.extracted_text, entity_extraction_url_analysis.extracted_text_length, entity_extraction_url_analysis.extracted_entities, entity_extraction_url_analysis.processing_method, entity_extraction_url_analysis.processing_status, entity_extraction_url_analysis.error_message, entity_extraction_url_analysis.created_at, entity_extraction_url_analysis.processed_at, entity_extraction_url_analysis.org_id 
FROM entity_extraction_url_analysis 
WHERE entity_extraction_url_analysis.analysis_id = %(analysis_id_1)s AND entity_extraction_url_analysis.url = %(url_1)s
2025-08-03 17:06:49,021 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_url_analysis.id, entity_extraction_url_analysis.analysis_id, entity_extraction_url_analysis.entity_analysis_id, entity_extraction_url_analysis.scrape_request_ref_id, entity_extraction_url_analysis.url, entity_extraction_url_analysis.url_type, entity_extraction_url_analysis.url_classification, entity_extraction_url_analysis.url_source_table, entity_extraction_url_analysis.is_reachable_by_gemini, entity_extraction_url_analysis.soft_classification, entity_extraction_url_analysis.hard_classification, entity_extraction_url_analysis.extracted_text, entity_extraction_url_analysis.extracted_text_length, entity_extraction_url_analysis.extracted_entities, entity_extraction_url_analysis.processing_method, entity_extraction_url_analysis.processing_status, entity_extraction_url_analysis.error_message, entity_extraction_url_analysis.created_at, entity_extraction_url_analysis.processed_at, entity_extraction_url_analysis.org_id 
FROM entity_extraction_url_analysis 
WHERE entity_extraction_url_analysis.analysis_id = %(analysis_id_1)s AND entity_extraction_url_analysis.url = %(url_1)s
2025-08-03 17:06:49,021 INFO sqlalchemy.engine.Engine [cached since 1.234s ago] {'analysis_id_1': 168, 'url_1': 'not_found'}
2025-08-03 17:06:49,021 - sqlalchemy.engine.Engine - INFO - [cached since 1.234s ago] {'analysis_id_1': 168, 'url_1': 'not_found'}
[2025-08-03 17:06:49][EntityExtractor][None][************************************] WARNING: URL analysis record not found for URL: not_found
2025-08-03 17:06:49,155 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:06:49,155 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:06:49,156 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:49,156 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:49,156 INFO sqlalchemy.engine.Engine [cached since 39.08s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T17:06:49.089731', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "URL analysis record not found for URL: not_found", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:49.089722", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:49,156 - sqlalchemy.engine.Engine - INFO - [cached since 39.08s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T17:06:49.089731', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "URL analysis record not found for URL: not_found", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:49.089722", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:49,231 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 17:06:49,231 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 17:06:49,367 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-03 17:06:49,367 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-03 17:06:49,535 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:06:49,535 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:06:49,536 INFO sqlalchemy.engine.Engine SELECT entity_extraction_url_analysis.id, entity_extraction_url_analysis.analysis_id, entity_extraction_url_analysis.entity_analysis_id, entity_extraction_url_analysis.scrape_request_ref_id, entity_extraction_url_analysis.url, entity_extraction_url_analysis.url_type, entity_extraction_url_analysis.url_classification, entity_extraction_url_analysis.url_source_table, entity_extraction_url_analysis.is_reachable_by_gemini, entity_extraction_url_analysis.soft_classification, entity_extraction_url_analysis.hard_classification, entity_extraction_url_analysis.extracted_text, entity_extraction_url_analysis.extracted_text_length, entity_extraction_url_analysis.extracted_entities, entity_extraction_url_analysis.processing_method, entity_extraction_url_analysis.processing_status, entity_extraction_url_analysis.error_message, entity_extraction_url_analysis.created_at, entity_extraction_url_analysis.processed_at, entity_extraction_url_analysis.org_id 
FROM entity_extraction_url_analysis 
WHERE entity_extraction_url_analysis.analysis_id = %(analysis_id_1)s AND entity_extraction_url_analysis.url = %(url_1)s
2025-08-03 17:06:49,536 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_url_analysis.id, entity_extraction_url_analysis.analysis_id, entity_extraction_url_analysis.entity_analysis_id, entity_extraction_url_analysis.scrape_request_ref_id, entity_extraction_url_analysis.url, entity_extraction_url_analysis.url_type, entity_extraction_url_analysis.url_classification, entity_extraction_url_analysis.url_source_table, entity_extraction_url_analysis.is_reachable_by_gemini, entity_extraction_url_analysis.soft_classification, entity_extraction_url_analysis.hard_classification, entity_extraction_url_analysis.extracted_text, entity_extraction_url_analysis.extracted_text_length, entity_extraction_url_analysis.extracted_entities, entity_extraction_url_analysis.processing_method, entity_extraction_url_analysis.processing_status, entity_extraction_url_analysis.error_message, entity_extraction_url_analysis.created_at, entity_extraction_url_analysis.processed_at, entity_extraction_url_analysis.org_id 
FROM entity_extraction_url_analysis 
WHERE entity_extraction_url_analysis.analysis_id = %(analysis_id_1)s AND entity_extraction_url_analysis.url = %(url_1)s
2025-08-03 17:06:49,536 INFO sqlalchemy.engine.Engine [cached since 1.749s ago] {'analysis_id_1': 168, 'url_1': 'https://www.shell.in/about-us/contact-us.html'}
2025-08-03 17:06:49,536 - sqlalchemy.engine.Engine - INFO - [cached since 1.749s ago] {'analysis_id_1': 168, 'url_1': 'https://www.shell.in/about-us/contact-us.html'}
[2025-08-03 17:06:49][EntityExtractor][None][************************************] WARNING: URL analysis record not found for URL: https://www.shell.in/about-us/contact-us.html
2025-08-03 17:06:49,670 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:06:49,670 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:06:49,671 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:49,671 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:49,671 INFO sqlalchemy.engine.Engine [cached since 39.59s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T17:06:49.618275', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "URL analysis record not found for URL: https://www.shell.in/about-us/contact-us.html", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:49.618268", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:49,671 - sqlalchemy.engine.Engine - INFO - [cached since 39.59s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T17:06:49.618275', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "URL analysis record not found for URL: https://www.shell.in/about-us/contact-us.html", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:49.618268", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:49,770 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 17:06:49,770 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 17:06:49,879 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-03 17:06:49,879 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-03 17:06:50,090 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:06:50,090 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:06:50,090 INFO sqlalchemy.engine.Engine SELECT entity_extraction_url_analysis.id, entity_extraction_url_analysis.analysis_id, entity_extraction_url_analysis.entity_analysis_id, entity_extraction_url_analysis.scrape_request_ref_id, entity_extraction_url_analysis.url, entity_extraction_url_analysis.url_type, entity_extraction_url_analysis.url_classification, entity_extraction_url_analysis.url_source_table, entity_extraction_url_analysis.is_reachable_by_gemini, entity_extraction_url_analysis.soft_classification, entity_extraction_url_analysis.hard_classification, entity_extraction_url_analysis.extracted_text, entity_extraction_url_analysis.extracted_text_length, entity_extraction_url_analysis.extracted_entities, entity_extraction_url_analysis.processing_method, entity_extraction_url_analysis.processing_status, entity_extraction_url_analysis.error_message, entity_extraction_url_analysis.created_at, entity_extraction_url_analysis.processed_at, entity_extraction_url_analysis.org_id 
FROM entity_extraction_url_analysis 
WHERE entity_extraction_url_analysis.analysis_id = %(analysis_id_1)s AND entity_extraction_url_analysis.url = %(url_1)s
2025-08-03 17:06:50,090 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_url_analysis.id, entity_extraction_url_analysis.analysis_id, entity_extraction_url_analysis.entity_analysis_id, entity_extraction_url_analysis.scrape_request_ref_id, entity_extraction_url_analysis.url, entity_extraction_url_analysis.url_type, entity_extraction_url_analysis.url_classification, entity_extraction_url_analysis.url_source_table, entity_extraction_url_analysis.is_reachable_by_gemini, entity_extraction_url_analysis.soft_classification, entity_extraction_url_analysis.hard_classification, entity_extraction_url_analysis.extracted_text, entity_extraction_url_analysis.extracted_text_length, entity_extraction_url_analysis.extracted_entities, entity_extraction_url_analysis.processing_method, entity_extraction_url_analysis.processing_status, entity_extraction_url_analysis.error_message, entity_extraction_url_analysis.created_at, entity_extraction_url_analysis.processed_at, entity_extraction_url_analysis.org_id 
FROM entity_extraction_url_analysis 
WHERE entity_extraction_url_analysis.analysis_id = %(analysis_id_1)s AND entity_extraction_url_analysis.url = %(url_1)s
2025-08-03 17:06:50,090 INFO sqlalchemy.engine.Engine [cached since 2.303s ago] {'analysis_id_1': 168, 'url_1': 'https://www.shell.in/about-us/careers.html'}
2025-08-03 17:06:50,090 - sqlalchemy.engine.Engine - INFO - [cached since 2.303s ago] {'analysis_id_1': 168, 'url_1': 'https://www.shell.in/about-us/careers.html'}
[2025-08-03 17:06:50][EntityExtractor][None][************************************] WARNING: URL analysis record not found for URL: https://www.shell.in/about-us/careers.html
2025-08-03 17:06:50,241 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:06:50,241 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:06:50,241 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:50,241 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:50,241 INFO sqlalchemy.engine.Engine [cached since 40.16s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T17:06:50.159045', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "URL analysis record not found for URL: https://www.shell.in/about-us/careers.html", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:50.159039", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:50,241 - sqlalchemy.engine.Engine - INFO - [cached since 40.16s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T17:06:50.159045', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "URL analysis record not found for URL: https://www.shell.in/about-us/careers.html", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:50.159039", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:50,310 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 17:06:50,310 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 17:06:50,425 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-03 17:06:50,425 - sqlalchemy.engine.Engine - INFO - ROLLBACK
[2025-08-03 17:06:50][EntityExtractor][168][************************************] INFO: Gemini extraction rate 72.7% < 90%, forcing backup flow for all fields
2025-08-03 17:06:50,585 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:06:50,585 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:06:50,586 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:50,586 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:50,586 INFO sqlalchemy.engine.Engine [cached since 40.51s ago] {'analysis_id': 168, 'timestamp': '2025-08-03T17:06:50.531727', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Gemini extraction rate 72.7% < 90%, forcing backup flow for all fields", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:50.531720", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:50,586 - sqlalchemy.engine.Engine - INFO - [cached since 40.51s ago] {'analysis_id': 168, 'timestamp': '2025-08-03T17:06:50.531727', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Gemini extraction rate 72.7% < 90%, forcing backup flow for all fields", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:50.531720", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:50,660 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 17:06:50,660 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 17:06:50][EntityExtractor][168][************************************] INFO: Gemini missing fields to fallback: ['shipping_countries', 'shipping_policy_details', 'jurisdiction_details']
2025-08-03 17:06:50,965 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:06:50,965 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:06:50,966 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:50,966 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:50,966 INFO sqlalchemy.engine.Engine [cached since 40.89s ago] {'analysis_id': 168, 'timestamp': '2025-08-03T17:06:50.869425', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Gemini missing fields to fallback: [\'shipping_countries\', \'shipping_policy_details\', \'jurisdiction_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:50.869417", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:50,966 - sqlalchemy.engine.Engine - INFO - [cached since 40.89s ago] {'analysis_id': 168, 'timestamp': '2025-08-03T17:06:50.869425', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Gemini missing fields to fallback: [\'shipping_countries\', \'shipping_policy_details\', \'jurisdiction_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:50.869417", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:51,030 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 17:06:51,030 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 17:06:51][EntityExtractor][168][************************************] INFO: Starting OpenAI backup flow for missing fields: ['shipping_countries', 'shipping_policy_details', 'jurisdiction_details']
2025-08-03 17:06:51,309 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:06:51,309 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:06:51,309 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:51,309 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:51,310 INFO sqlalchemy.engine.Engine [cached since 41.23s ago] {'analysis_id': 168, 'timestamp': '2025-08-03T17:06:51.205746', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Starting OpenAI backup flow for missing fields: [\'shipping_countries\', \'shipping_policy_details\', \'jurisdiction_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:51.205739", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:51,310 - sqlalchemy.engine.Engine - INFO - [cached since 41.23s ago] {'analysis_id': 168, 'timestamp': '2025-08-03T17:06:51.205746', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Starting OpenAI backup flow for missing fields: [\'shipping_countries\', \'shipping_policy_details\', \'jurisdiction_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:51.205739", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:51,376 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 17:06:51,376 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 17:06:51,550 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:06:51,550 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:06:51,553 INFO sqlalchemy.engine.Engine SELECT policy_analysis_new_gemini.id, policy_analysis_new_gemini.website, policy_analysis_new_gemini.scrape_request_ref_id, policy_analysis_new_gemini.analysis_flow_used, policy_analysis_new_gemini.reachability_percentage, policy_analysis_new_gemini.total_urls_processed, policy_analysis_new_gemini.privacy_policy_url, policy_analysis_new_gemini.terms_and_condition_url, policy_analysis_new_gemini.shipping_delivery_url, policy_analysis_new_gemini.returns_cancellation_exchange_url, policy_analysis_new_gemini.contact_us_url, policy_analysis_new_gemini.home_page_url, policy_analysis_new_gemini.about_us_url, policy_analysis_new_gemini.privacy_policy_text, policy_analysis_new_gemini.terms_and_condition_text, policy_analysis_new_gemini.shipping_delivery_text, policy_analysis_new_gemini.returns_cancellation_exchange_text, policy_analysis_new_gemini.contact_us_text, policy_analysis_new_gemini.home_page_text, policy_analysis_new_gemini.about_us_text, policy_analysis_new_gemini.privacy_policy_screenshot, policy_analysis_new_gemini.terms_and_condition_screenshot, policy_analysis_new_gemini.shipping_delivery_screenshot, policy_analysis_new_gemini.returns_cancellation_exchange_screenshot, policy_analysis_new_gemini.contact_us_screenshot, policy_analysis_new_gemini.home_page_screenshot, policy_analysis_new_gemini.about_us_screenshot, policy_analysis_new_gemini.instagram_url, policy_analysis_new_gemini.instagram_text, policy_analysis_new_gemini.instagram_screenshot, policy_analysis_new_gemini.youtube_url, policy_analysis_new_gemini.youtube_text, policy_analysis_new_gemini.youtube_screenshot, policy_analysis_new_gemini.facebook_url, policy_analysis_new_gemini.facebook_text, policy_analysis_new_gemini.facebook_screenshot, policy_analysis_new_gemini.twitter_url, policy_analysis_new_gemini.twitter_text, policy_analysis_new_gemini.twitter_screenshot, policy_analysis_new_gemini.linkedin_url, policy_analysis_new_gemini.linkedin_text, policy_analysis_new_gemini.linkedin_screenshot, policy_analysis_new_gemini.pinterest_url, policy_analysis_new_gemini.pinterest_text, policy_analysis_new_gemini.pinterest_screenshot, policy_analysis_new_gemini.x_url, policy_analysis_new_gemini.x_text, policy_analysis_new_gemini.x_screenshot, policy_analysis_new_gemini.result_status, policy_analysis_new_gemini.failed_at, policy_analysis_new_gemini.last_updated, policy_analysis_new_gemini.details, policy_analysis_new_gemini.processing_status, policy_analysis_new_gemini.created_at, policy_analysis_new_gemini.started_at, policy_analysis_new_gemini.completed_at, policy_analysis_new_gemini.error_message, policy_analysis_new_gemini.org_id 
FROM policy_analysis_new_gemini 
WHERE policy_analysis_new_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s ORDER BY policy_analysis_new_gemini.id DESC
2025-08-03 17:06:51,553 - sqlalchemy.engine.Engine - INFO - SELECT policy_analysis_new_gemini.id, policy_analysis_new_gemini.website, policy_analysis_new_gemini.scrape_request_ref_id, policy_analysis_new_gemini.analysis_flow_used, policy_analysis_new_gemini.reachability_percentage, policy_analysis_new_gemini.total_urls_processed, policy_analysis_new_gemini.privacy_policy_url, policy_analysis_new_gemini.terms_and_condition_url, policy_analysis_new_gemini.shipping_delivery_url, policy_analysis_new_gemini.returns_cancellation_exchange_url, policy_analysis_new_gemini.contact_us_url, policy_analysis_new_gemini.home_page_url, policy_analysis_new_gemini.about_us_url, policy_analysis_new_gemini.privacy_policy_text, policy_analysis_new_gemini.terms_and_condition_text, policy_analysis_new_gemini.shipping_delivery_text, policy_analysis_new_gemini.returns_cancellation_exchange_text, policy_analysis_new_gemini.contact_us_text, policy_analysis_new_gemini.home_page_text, policy_analysis_new_gemini.about_us_text, policy_analysis_new_gemini.privacy_policy_screenshot, policy_analysis_new_gemini.terms_and_condition_screenshot, policy_analysis_new_gemini.shipping_delivery_screenshot, policy_analysis_new_gemini.returns_cancellation_exchange_screenshot, policy_analysis_new_gemini.contact_us_screenshot, policy_analysis_new_gemini.home_page_screenshot, policy_analysis_new_gemini.about_us_screenshot, policy_analysis_new_gemini.instagram_url, policy_analysis_new_gemini.instagram_text, policy_analysis_new_gemini.instagram_screenshot, policy_analysis_new_gemini.youtube_url, policy_analysis_new_gemini.youtube_text, policy_analysis_new_gemini.youtube_screenshot, policy_analysis_new_gemini.facebook_url, policy_analysis_new_gemini.facebook_text, policy_analysis_new_gemini.facebook_screenshot, policy_analysis_new_gemini.twitter_url, policy_analysis_new_gemini.twitter_text, policy_analysis_new_gemini.twitter_screenshot, policy_analysis_new_gemini.linkedin_url, policy_analysis_new_gemini.linkedin_text, policy_analysis_new_gemini.linkedin_screenshot, policy_analysis_new_gemini.pinterest_url, policy_analysis_new_gemini.pinterest_text, policy_analysis_new_gemini.pinterest_screenshot, policy_analysis_new_gemini.x_url, policy_analysis_new_gemini.x_text, policy_analysis_new_gemini.x_screenshot, policy_analysis_new_gemini.result_status, policy_analysis_new_gemini.failed_at, policy_analysis_new_gemini.last_updated, policy_analysis_new_gemini.details, policy_analysis_new_gemini.processing_status, policy_analysis_new_gemini.created_at, policy_analysis_new_gemini.started_at, policy_analysis_new_gemini.completed_at, policy_analysis_new_gemini.error_message, policy_analysis_new_gemini.org_id 
FROM policy_analysis_new_gemini 
WHERE policy_analysis_new_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s ORDER BY policy_analysis_new_gemini.id DESC
2025-08-03 17:06:51,554 INFO sqlalchemy.engine.Engine [generated in 0.00016s] {'scrape_request_ref_id_1': '************************************'}
2025-08-03 17:06:51,554 - sqlalchemy.engine.Engine - INFO - [generated in 0.00016s] {'scrape_request_ref_id_1': '************************************'}
[2025-08-03 17:06:51][EntityExtractor][168][************************************] INFO: Retrieved policy texts: ['privacy_policy', 'terms_and_condition', 'contact_us', 'about_us']
2025-08-03 17:06:51,770 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:06:51,770 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:06:51,771 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:51,771 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:51,771 INFO sqlalchemy.engine.Engine [cached since 41.69s ago] {'analysis_id': 168, 'timestamp': '2025-08-03T17:06:51.715508', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Retrieved policy texts: [\'privacy_policy\', \'terms_and_condition\', \'contact_us\', \'about_us\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:51.715501", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:51,771 - sqlalchemy.engine.Engine - INFO - [cached since 41.69s ago] {'analysis_id': 168, 'timestamp': '2025-08-03T17:06:51.715508', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Retrieved policy texts: [\'privacy_policy\', \'terms_and_condition\', \'contact_us\', \'about_us\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:51.715501", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:51,855 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 17:06:51,855 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 17:06:51,975 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-03 17:06:51,975 - sqlalchemy.engine.Engine - INFO - ROLLBACK
[2025-08-03 17:06:52][EntityExtractor][168][************************************] INFO: Found policy texts for 4 policy types
2025-08-03 17:06:52,145 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:06:52,145 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:06:52,146 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:52,146 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:52,146 INFO sqlalchemy.engine.Engine [cached since 42.07s ago] {'analysis_id': 168, 'timestamp': '2025-08-03T17:06:52.090789', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found policy texts for 4 policy types", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:52.090778", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:52,146 - sqlalchemy.engine.Engine - INFO - [cached since 42.07s ago] {'analysis_id': 168, 'timestamp': '2025-08-03T17:06:52.090789', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found policy texts for 4 policy types", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:52.090778", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:52,215 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 17:06:52,215 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 17:06:52][EntityExtractor][168][************************************] INFO: Processing legal_info with fields: ['jurisdiction_details']
2025-08-03 17:06:52,510 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:06:52,510 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:06:52,510 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:52,510 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:52,510 INFO sqlalchemy.engine.Engine [cached since 42.43s ago] {'analysis_id': 168, 'timestamp': '2025-08-03T17:06:52.458022', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Processing legal_info with fields: [\'jurisdiction_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:52.458011", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:52,510 - sqlalchemy.engine.Engine - INFO - [cached since 42.43s ago] {'analysis_id': 168, 'timestamp': '2025-08-03T17:06:52.458022', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Processing legal_info with fields: [\'jurisdiction_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:52.458011", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:52,565 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 17:06:52,565 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 17:06:54,236 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 17:06:54,305 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:06:54,305 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:06:54,306 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:54,306 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:54,306 INFO sqlalchemy.engine.Engine [cached since 44.23s ago] {'analysis_id': 0, 'timestamp': '2025-08-03 17:06:52', 'type': 'openai', 'messages': '[\n    {\n        "role": "user",\n        "content": "\\nYou are an expert at extracting business information from policy documents and website cont ... (11946 characters truncated) ... \n    \\"jurisdiction_details\\": \\"extracted jurisdiction details or null\\"\\n}\\n\\nReturn only the JSON response, no additional text."\n    }\n]', 'response': '"{\\n    \\"jurisdiction_details\\": \\"Indian company, governed by the laws of India\\"\\n}"', 'org_id': 'default'}
2025-08-03 17:06:54,306 - sqlalchemy.engine.Engine - INFO - [cached since 44.23s ago] {'analysis_id': 0, 'timestamp': '2025-08-03 17:06:52', 'type': 'openai', 'messages': '[\n    {\n        "role": "user",\n        "content": "\\nYou are an expert at extracting business information from policy documents and website cont ... (11946 characters truncated) ... \n    \\"jurisdiction_details\\": \\"extracted jurisdiction details or null\\"\\n}\\n\\nReturn only the JSON response, no additional text."\n    }\n]', 'response': '"{\\n    \\"jurisdiction_details\\": \\"Indian company, governed by the laws of India\\"\\n}"', 'org_id': 'default'}
2025-08-03 17:06:54,401 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 17:06:54,401 - sqlalchemy.engine.Engine - INFO - COMMIT
API response logged to: api_logs/openai_20250803_170654_dcc15666.json
[2025-08-03 17:06:54][EntityExtractor][168][************************************] INFO: OpenAI legal_info call extracted 1 fields: ['jurisdiction_details']
2025-08-03 17:06:54,595 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:06:54,595 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:06:54,595 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:54,595 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:54,596 INFO sqlalchemy.engine.Engine [cached since 44.52s ago] {'analysis_id': 168, 'timestamp': '2025-08-03T17:06:54.531797', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "OpenAI legal_info call extracted 1 fields: [\'jurisdiction_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:54.531792", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:54,596 - sqlalchemy.engine.Engine - INFO - [cached since 44.52s ago] {'analysis_id': 168, 'timestamp': '2025-08-03T17:06:54.531797', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "OpenAI legal_info call extracted 1 fields: [\'jurisdiction_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:54.531792", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:54,680 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 17:06:54,680 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 17:06:54][EntityExtractor][168][************************************] INFO: Processing shipping_info with fields: ['shipping_countries', 'shipping_policy_details']
2025-08-03 17:06:54,857 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:06:54,857 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:06:54,857 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:54,857 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:54,857 INFO sqlalchemy.engine.Engine [cached since 44.78s ago] {'analysis_id': 168, 'timestamp': '2025-08-03T17:06:54.800873', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Processing shipping_info with fields: [\'shipping_countries\', \'shipping_policy_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:54.800863", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:54,857 - sqlalchemy.engine.Engine - INFO - [cached since 44.78s ago] {'analysis_id': 168, 'timestamp': '2025-08-03T17:06:54.800873', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Processing shipping_info with fields: [\'shipping_countries\', \'shipping_policy_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:54.800863", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:54,936 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 17:06:54,936 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 17:06:56,620 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 17:06:56,680 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:06:56,680 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:06:56,681 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:56,681 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:56,681 INFO sqlalchemy.engine.Engine [cached since 46.6s ago] {'analysis_id': 0, 'timestamp': '2025-08-03 17:06:55', 'type': 'openai', 'messages': '[\n    {\n        "role": "user",\n        "content": "\\nYou are an expert at extracting business information from policy documents and website cont ... (2726 characters truncated) ... \\"shipping_policy_details\\": \\"extracted shipping policy details or null\\"\\n}\\n\\nReturn only the JSON response, no additional text."\n    }\n]', 'response': '"{\\n    \\"shipping_countries\\": \\"null\\",\\n    \\"shipping_policy_details\\": \\"null\\"\\n}"', 'org_id': 'default'}
2025-08-03 17:06:56,681 - sqlalchemy.engine.Engine - INFO - [cached since 46.6s ago] {'analysis_id': 0, 'timestamp': '2025-08-03 17:06:55', 'type': 'openai', 'messages': '[\n    {\n        "role": "user",\n        "content": "\\nYou are an expert at extracting business information from policy documents and website cont ... (2726 characters truncated) ... \\"shipping_policy_details\\": \\"extracted shipping policy details or null\\"\\n}\\n\\nReturn only the JSON response, no additional text."\n    }\n]', 'response': '"{\\n    \\"shipping_countries\\": \\"null\\",\\n    \\"shipping_policy_details\\": \\"null\\"\\n}"', 'org_id': 'default'}
2025-08-03 17:06:56,753 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 17:06:56,753 - sqlalchemy.engine.Engine - INFO - COMMIT
API response logged to: api_logs/openai_20250803_170656_fa7ddc97.json
[2025-08-03 17:06:56][EntityExtractor][168][************************************] INFO: OpenAI shipping_info call extracted 0 fields: []
2025-08-03 17:06:57,075 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:06:57,075 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:06:57,075 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:57,075 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:57,075 INFO sqlalchemy.engine.Engine [cached since 47s ago] {'analysis_id': 168, 'timestamp': '2025-08-03T17:06:56.918398', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "OpenAI shipping_info call extracted 0 fields: []", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:56.918391", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:57,075 - sqlalchemy.engine.Engine - INFO - [cached since 47s ago] {'analysis_id': 168, 'timestamp': '2025-08-03T17:06:56.918398', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "OpenAI shipping_info call extracted 0 fields: []", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:56.918391", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:57,125 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 17:06:57,125 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 17:06:57][EntityExtractor][168][************************************] INFO: Making final comprehensive call for remaining fields: ['shipping_countries', 'shipping_policy_details']
2025-08-03 17:06:57,349 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:06:57,349 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:06:57,349 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:57,349 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:57,349 INFO sqlalchemy.engine.Engine [cached since 47.27s ago] {'analysis_id': 168, 'timestamp': '2025-08-03T17:06:57.269830', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Making final comprehensive call for remaining fields: [\'shipping_countries\', \'shipping_policy_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:57.269816", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:57,349 - sqlalchemy.engine.Engine - INFO - [cached since 47.27s ago] {'analysis_id': 168, 'timestamp': '2025-08-03T17:06:57.269830', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Making final comprehensive call for remaining fields: [\'shipping_countries\', \'shipping_policy_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:57.269816", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:57,455 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 17:06:57,455 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 17:06:59,415 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 17:06:59,487 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:06:59,487 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:06:59,488 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:59,488 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:59,488 INFO sqlalchemy.engine.Engine [cached since 49.41s ago] {'analysis_id': 0, 'timestamp': '2025-08-03 17:06:57', 'type': 'openai', 'messages': '[\n    {\n        "role": "user",\n        "content": "\\nYou are an expert at extracting business information from policy documents and website cont ... (14443 characters truncated) ... \\"shipping_policy_details\\": \\"extracted shipping policy details or null\\"\\n}\\n\\nReturn only the JSON response, no additional text."\n    }\n]', 'response': '"{\\n    \\"shipping_countries\\": null,\\n    \\"shipping_policy_details\\": null\\n}"', 'org_id': 'default'}
2025-08-03 17:06:59,488 - sqlalchemy.engine.Engine - INFO - [cached since 49.41s ago] {'analysis_id': 0, 'timestamp': '2025-08-03 17:06:57', 'type': 'openai', 'messages': '[\n    {\n        "role": "user",\n        "content": "\\nYou are an expert at extracting business information from policy documents and website cont ... (14443 characters truncated) ... \\"shipping_policy_details\\": \\"extracted shipping policy details or null\\"\\n}\\n\\nReturn only the JSON response, no additional text."\n    }\n]', 'response': '"{\\n    \\"shipping_countries\\": null,\\n    \\"shipping_policy_details\\": null\\n}"', 'org_id': 'default'}
2025-08-03 17:06:59,639 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 17:06:59,639 - sqlalchemy.engine.Engine - INFO - COMMIT
API response logged to: api_logs/openai_20250803_170659_17fbe623.json
[2025-08-03 17:06:59][EntityExtractor][168][************************************] INFO: OpenAI comprehensive call extracted 0 fields: []
2025-08-03 17:06:59,805 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:06:59,805 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:06:59,806 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:59,806 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:06:59,806 INFO sqlalchemy.engine.Engine [cached since 49.73s ago] {'analysis_id': 168, 'timestamp': '2025-08-03T17:06:59.756735', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "OpenAI comprehensive call extracted 0 fields: []", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:59.756728", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:59,806 - sqlalchemy.engine.Engine - INFO - [cached since 49.73s ago] {'analysis_id': 168, 'timestamp': '2025-08-03T17:06:59.756735', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "OpenAI comprehensive call extracted 0 fields: []", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:06:59.756728", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:06:59,927 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 17:06:59,927 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 17:07:00][EntityExtractor][168][************************************] INFO: Total OpenAI backup extracted 1 fields: ['jurisdiction_details']
2025-08-03 17:07:00,283 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:07:00,283 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:07:00,283 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:07:00,283 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:07:00,283 INFO sqlalchemy.engine.Engine [cached since 50.21s ago] {'analysis_id': 168, 'timestamp': '2025-08-03T17:07:00.193423', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Total OpenAI backup extracted 1 fields: [\'jurisdiction_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:07:00.193415", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:07:00,283 - sqlalchemy.engine.Engine - INFO - [cached since 50.21s ago] {'analysis_id': 168, 'timestamp': '2025-08-03T17:07:00.193423', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Total OpenAI backup extracted 1 fields: [\'jurisdiction_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:07:00.193415", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:07:00,343 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 17:07:00,343 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 17:07:00][EntityExtractor][168][************************************] INFO: OpenAI backup extracted: ['jurisdiction_details']
2025-08-03 17:07:00,565 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:07:00,565 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:07:00,565 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:07:00,565 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:07:00,566 INFO sqlalchemy.engine.Engine [cached since 50.49s ago] {'analysis_id': 168, 'timestamp': '2025-08-03T17:07:00.486362', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "OpenAI backup extracted: [\'jurisdiction_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:07:00.486355", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:07:00,566 - sqlalchemy.engine.Engine - INFO - [cached since 50.49s ago] {'analysis_id': 168, 'timestamp': '2025-08-03T17:07:00.486362', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "OpenAI backup extracted: [\'jurisdiction_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:07:00.486355", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:07:00,669 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 17:07:00,669 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 17:07:00][EntityExtractor][168][************************************] INFO: Merging 2 AI extraction results
2025-08-03 17:07:00,964 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:07:00,964 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:07:00,965 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:07:00,965 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:07:00,965 INFO sqlalchemy.engine.Engine [cached since 50.89s ago] {'analysis_id': 168, 'timestamp': '2025-08-03T17:07:00.873523', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Merging 2 AI extraction results", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:07:00.873516", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:07:00,965 - sqlalchemy.engine.Engine - INFO - [cached since 50.89s ago] {'analysis_id': 168, 'timestamp': '2025-08-03T17:07:00.873523', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Merging 2 AI extraction results", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:07:00.873516", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:07:01,026 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 17:07:01,026 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 17:07:01][EntityExtractor][168][************************************] INFO: Merged result contains 8 fields
2025-08-03 17:07:01,353 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:07:01,353 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:07:01,353 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:07:01,353 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:07:01,353 INFO sqlalchemy.engine.Engine [cached since 51.28s ago] {'analysis_id': 168, 'timestamp': '2025-08-03T17:07:01.206531', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Merged result contains 8 fields", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:07:01.206524", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:07:01,353 - sqlalchemy.engine.Engine - INFO - [cached since 51.28s ago] {'analysis_id': 168, 'timestamp': '2025-08-03T17:07:01.206531', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Merged result contains 8 fields", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:07:01.206524", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:07:01,430 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 17:07:01,430 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 17:07:01][EntityExtractor][168][************************************] INFO: Final merged extraction result keys: ['legal_name', 'business_email', 'support_email', 'business_contact_numbers', 'business_location', 'has_jurisdiction_law', 'jurisdiction_place', 'jurisdiction_details']
2025-08-03 17:07:01,605 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:07:01,605 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:07:01,606 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:07:01,606 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:07:01,606 INFO sqlalchemy.engine.Engine [cached since 51.53s ago] {'analysis_id': 168, 'timestamp': '2025-08-03T17:07:01.550902', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Final merged extraction result keys: [\'legal_name\', \'business_email\', \'support_email\', \'business_contact_numbers ... (75 characters truncated) ... , \'jurisdiction_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:07:01.550894", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:07:01,606 - sqlalchemy.engine.Engine - INFO - [cached since 51.53s ago] {'analysis_id': 168, 'timestamp': '2025-08-03T17:07:01.550902', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Final merged extraction result keys: [\'legal_name\', \'business_email\', \'support_email\', \'business_contact_numbers ... (75 characters truncated) ... , \'jurisdiction_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:07:01.550894", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:07:01,675 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 17:07:01,675 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 17:07:01][EntityExtractor][168][************************************] INFO: Fallback raw text for privacy_policy_text: present
2025-08-03 17:07:01,850 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:07:01,850 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:07:01,853 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:07:01,853 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:07:01,853 INFO sqlalchemy.engine.Engine [cached since 51.78s ago] {'analysis_id': 168, 'timestamp': '2025-08-03T17:07:01.799969', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Fallback raw text for privacy_policy_text: present", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:07:01.799962", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:07:01,853 - sqlalchemy.engine.Engine - INFO - [cached since 51.78s ago] {'analysis_id': 168, 'timestamp': '2025-08-03T17:07:01.799969', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Fallback raw text for privacy_policy_text: present", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:07:01.799962", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:07:01,900 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 17:07:01,900 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 17:07:02][EntityExtractor][168][************************************] INFO: After fallback, merged_result[privacy_policy_text] length: 2340
2025-08-03 17:07:02,085 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:07:02,085 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:07:02,085 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:07:02,085 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:07:02,085 INFO sqlalchemy.engine.Engine [cached since 52.01s ago] {'analysis_id': 168, 'timestamp': '2025-08-03T17:07:02.031051', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "After fallback, merged_result[privacy_policy_text] length: 2340", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:07:02.031043", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:07:02,085 - sqlalchemy.engine.Engine - INFO - [cached since 52.01s ago] {'analysis_id': 168, 'timestamp': '2025-08-03T17:07:02.031051', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "After fallback, merged_result[privacy_policy_text] length: 2340", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:07:02.031043", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:07:02,146 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 17:07:02,146 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 17:07:02][EntityExtractor][168][************************************] INFO: Fallback raw text for terms_conditions_text: present
2025-08-03 17:07:02,333 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:07:02,333 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:07:02,333 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:07:02,333 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:07:02,333 INFO sqlalchemy.engine.Engine [cached since 52.26s ago] {'analysis_id': 168, 'timestamp': '2025-08-03T17:07:02.266929', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Fallback raw text for terms_conditions_text: present", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:07:02.266922", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:07:02,333 - sqlalchemy.engine.Engine - INFO - [cached since 52.26s ago] {'analysis_id': 168, 'timestamp': '2025-08-03T17:07:02.266929', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Fallback raw text for terms_conditions_text: present", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:07:02.266922", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:07:02,410 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 17:07:02,410 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 17:07:02][EntityExtractor][168][************************************] INFO: After fallback, merged_result[terms_conditions_text] length: 1479
2025-08-03 17:07:02,590 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:07:02,590 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:07:02,591 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:07:02,591 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:07:02,591 INFO sqlalchemy.engine.Engine [cached since 52.51s ago] {'analysis_id': 168, 'timestamp': '2025-08-03T17:07:02.523441', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "After fallback, merged_result[terms_conditions_text] length: 1479", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:07:02.523434", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:07:02,591 - sqlalchemy.engine.Engine - INFO - [cached since 52.51s ago] {'analysis_id': 168, 'timestamp': '2025-08-03T17:07:02.523441', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "After fallback, merged_result[terms_conditions_text] length: 1479", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:07:02.523434", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:07:02,676 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 17:07:02,676 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 17:07:02][EntityExtractor][168][************************************] INFO: Before database storage - jurisdiction_details: Indian company, governed by the laws of India
2025-08-03 17:07:02,860 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:07:02,860 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:07:02,861 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:07:02,861 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:07:02,861 INFO sqlalchemy.engine.Engine [cached since 52.78s ago] {'analysis_id': 168, 'timestamp': '2025-08-03T17:07:02.800829', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Before database storage - jurisdiction_details: Indian company, governed by the laws of India", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:07:02.800822", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:07:02,861 - sqlalchemy.engine.Engine - INFO - [cached since 52.78s ago] {'analysis_id': 168, 'timestamp': '2025-08-03T17:07:02.800829', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Before database storage - jurisdiction_details: Indian company, governed by the laws of India", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:07:02.800822", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:07:02,932 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 17:07:02,932 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 17:07:03][EntityExtractor][168][************************************] INFO: Before database storage - jurisdiction_place: ['India']
2025-08-03 17:07:03,105 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:07:03,105 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:07:03,105 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:07:03,105 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:07:03,106 INFO sqlalchemy.engine.Engine [cached since 53.03s ago] {'analysis_id': 168, 'timestamp': '2025-08-03T17:07:03.050948', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Before database storage - jurisdiction_place: [\'India\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:07:03.050940", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:07:03,106 - sqlalchemy.engine.Engine - INFO - [cached since 53.03s ago] {'analysis_id': 168, 'timestamp': '2025-08-03T17:07:03.050948', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Before database storage - jurisdiction_place: [\'India\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:07:03.050940", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:07:03,175 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 17:07:03,175 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 17:07:03][EntityExtractor][168][************************************] INFO: Before database storage - has_jurisdiction_law: True
2025-08-03 17:07:03,373 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:07:03,373 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:07:03,374 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:07:03,374 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:07:03,374 INFO sqlalchemy.engine.Engine [cached since 53.3s ago] {'analysis_id': 168, 'timestamp': '2025-08-03T17:07:03.305761', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Before database storage - has_jurisdiction_law: True", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:07:03.305749", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:07:03,374 - sqlalchemy.engine.Engine - INFO - [cached since 53.3s ago] {'analysis_id': 168, 'timestamp': '2025-08-03T17:07:03.305761', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Before database storage - has_jurisdiction_law: True", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:07:03.305749", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:07:03,488 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 17:07:03,488 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 17:07:03,753 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:07:03,753 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:07:03,754 INFO sqlalchemy.engine.Engine INSERT INTO entity_extraction_analysis (scrape_request_ref_id, website_url, processing_status, legal_name, business_email, support_email, business_contact_numbers, business_location, has_jurisdiction_law, jurisdiction_details, accepts_international_orders, shipping_policy_details, jurisdiction_place, shipping_countries, privacy_policy_text, terms_conditions_text, urls_reachable_by_gemini, urls_not_reachable_by_gemini, extraction_method, total_urls_processed, all_urls_found, reachable_urls, unreachable_urls, policy_urls_matched, created_at, started_at, completed_at, error_message, org_id) VALUES (%(scrape_request_ref_id)s, %(website_url)s, %(processing_status)s, %(legal_name)s, %(business_email)s, %(support_email)s, %(business_contact_numbers)s, %(business_location)s, %(has_jurisdiction_law)s, %(jurisdiction_details)s, %(accepts_international_orders)s, %(shipping_policy_details)s, %(jurisdiction_place)s, %(shipping_countries)s, %(privacy_policy_text)s, %(terms_conditions_text)s, %(urls_reachable_by_gemini)s, %(urls_not_reachable_by_gemini)s, %(extraction_method)s, %(total_urls_processed)s, %(all_urls_found)s, %(reachable_urls)s, %(unreachable_urls)s, %(policy_urls_matched)s, %(created_at)s, %(started_at)s, %(completed_at)s, %(error_message)s, %(org_id)s)
2025-08-03 17:07:03,754 - sqlalchemy.engine.Engine - INFO - INSERT INTO entity_extraction_analysis (scrape_request_ref_id, website_url, processing_status, legal_name, business_email, support_email, business_contact_numbers, business_location, has_jurisdiction_law, jurisdiction_details, accepts_international_orders, shipping_policy_details, jurisdiction_place, shipping_countries, privacy_policy_text, terms_conditions_text, urls_reachable_by_gemini, urls_not_reachable_by_gemini, extraction_method, total_urls_processed, all_urls_found, reachable_urls, unreachable_urls, policy_urls_matched, created_at, started_at, completed_at, error_message, org_id) VALUES (%(scrape_request_ref_id)s, %(website_url)s, %(processing_status)s, %(legal_name)s, %(business_email)s, %(support_email)s, %(business_contact_numbers)s, %(business_location)s, %(has_jurisdiction_law)s, %(jurisdiction_details)s, %(accepts_international_orders)s, %(shipping_policy_details)s, %(jurisdiction_place)s, %(shipping_countries)s, %(privacy_policy_text)s, %(terms_conditions_text)s, %(urls_reachable_by_gemini)s, %(urls_not_reachable_by_gemini)s, %(extraction_method)s, %(total_urls_processed)s, %(all_urls_found)s, %(reachable_urls)s, %(unreachable_urls)s, %(policy_urls_matched)s, %(created_at)s, %(started_at)s, %(completed_at)s, %(error_message)s, %(org_id)s)
2025-08-03 17:07:03,754 INFO sqlalchemy.engine.Engine [generated in 0.00032s] {'scrape_request_ref_id': '************************************', 'website_url': 'https://www.shell.in', 'processing_status': 'COMPLETED', 'legal_name': 'Shell India Markets Private Limited', 'business_email': '<EMAIL>', 'support_email': '<EMAIL>', 'business_contact_numbers': '044-3099 1103, 044-4344 2650, +91 44 46945101', 'business_location': 'Commerzone, Block-2, No.2, 200 Feet Radial Road, Pallikaranai, Chennai – 600100 India', 'has_jurisdiction_law': True, 'jurisdiction_details': 'Indian company, governed by the laws of India', 'accepts_international_orders': None, 'shipping_policy_details': None, 'jurisdiction_place': 'India', 'shipping_countries': None, 'privacy_policy_text': None, 'terms_conditions_text': None, 'urls_reachable_by_gemini': '["https://www.shell.in/privacy.html", "https://www.shell.in/terms-and-conditions.html", "not_found", "https://www.shell.in/about-us/contact-us.html", "https://www.shell.in/about-us/careers.html"]', 'urls_not_reachable_by_gemini': None, 'extraction_method': 'mixed', 'total_urls_processed': 5, 'all_urls_found': None, 'reachable_urls': None, 'unreachable_urls': None, 'policy_urls_matched': None, 'created_at': '2025-08-03T17:07:03.700634', 'started_at': '2025-08-03T17:07:03.700623', 'completed_at': None, 'error_message': None, 'org_id': '2'}
2025-08-03 17:07:03,754 - sqlalchemy.engine.Engine - INFO - [generated in 0.00032s] {'scrape_request_ref_id': '************************************', 'website_url': 'https://www.shell.in', 'processing_status': 'COMPLETED', 'legal_name': 'Shell India Markets Private Limited', 'business_email': '<EMAIL>', 'support_email': '<EMAIL>', 'business_contact_numbers': '044-3099 1103, 044-4344 2650, +91 44 46945101', 'business_location': 'Commerzone, Block-2, No.2, 200 Feet Radial Road, Pallikaranai, Chennai – 600100 India', 'has_jurisdiction_law': True, 'jurisdiction_details': 'Indian company, governed by the laws of India', 'accepts_international_orders': None, 'shipping_policy_details': None, 'jurisdiction_place': 'India', 'shipping_countries': None, 'privacy_policy_text': None, 'terms_conditions_text': None, 'urls_reachable_by_gemini': '["https://www.shell.in/privacy.html", "https://www.shell.in/terms-and-conditions.html", "not_found", "https://www.shell.in/about-us/contact-us.html", "https://www.shell.in/about-us/careers.html"]', 'urls_not_reachable_by_gemini': None, 'extraction_method': 'mixed', 'total_urls_processed': 5, 'all_urls_found': None, 'reachable_urls': None, 'unreachable_urls': None, 'policy_urls_matched': None, 'created_at': '2025-08-03T17:07:03.700634', 'started_at': '2025-08-03T17:07:03.700623', 'completed_at': None, 'error_message': None, 'org_id': '2'}
2025-08-03 17:07:03,853 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 17:07:03,853 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 17:07:04,031 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:07:04,031 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:07:04,032 INFO sqlalchemy.engine.Engine SELECT entity_extraction_analysis.id, entity_extraction_analysis.scrape_request_ref_id, entity_extraction_analysis.website_url, entity_extraction_analysis.processing_status, entity_extraction_analysis.legal_name, entity_extraction_analysis.business_email, entity_extraction_analysis.support_email, entity_extraction_analysis.business_contact_numbers, entity_extraction_analysis.business_location, entity_extraction_analysis.has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details, entity_extraction_analysis.accepts_international_orders, entity_extraction_analysis.shipping_policy_details, entity_extraction_analysis.jurisdiction_place, entity_extraction_analysis.shipping_countries, entity_extraction_analysis.privacy_policy_text, entity_extraction_analysis.terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method, entity_extraction_analysis.total_urls_processed, entity_extraction_analysis.all_urls_found, entity_extraction_analysis.reachable_urls, entity_extraction_analysis.unreachable_urls, entity_extraction_analysis.policy_urls_matched, entity_extraction_analysis.created_at, entity_extraction_analysis.started_at, entity_extraction_analysis.completed_at, entity_extraction_analysis.error_message, entity_extraction_analysis.org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.id = %(pk_1)s
2025-08-03 17:07:04,032 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_analysis.id, entity_extraction_analysis.scrape_request_ref_id, entity_extraction_analysis.website_url, entity_extraction_analysis.processing_status, entity_extraction_analysis.legal_name, entity_extraction_analysis.business_email, entity_extraction_analysis.support_email, entity_extraction_analysis.business_contact_numbers, entity_extraction_analysis.business_location, entity_extraction_analysis.has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details, entity_extraction_analysis.accepts_international_orders, entity_extraction_analysis.shipping_policy_details, entity_extraction_analysis.jurisdiction_place, entity_extraction_analysis.shipping_countries, entity_extraction_analysis.privacy_policy_text, entity_extraction_analysis.terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method, entity_extraction_analysis.total_urls_processed, entity_extraction_analysis.all_urls_found, entity_extraction_analysis.reachable_urls, entity_extraction_analysis.unreachable_urls, entity_extraction_analysis.policy_urls_matched, entity_extraction_analysis.created_at, entity_extraction_analysis.started_at, entity_extraction_analysis.completed_at, entity_extraction_analysis.error_message, entity_extraction_analysis.org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.id = %(pk_1)s
2025-08-03 17:07:04,032 INFO sqlalchemy.engine.Engine [generated in 0.00014s] {'pk_1': 169}
2025-08-03 17:07:04,032 - sqlalchemy.engine.Engine - INFO - [generated in 0.00014s] {'pk_1': 169}
[2025-08-03 17:07:04][EntityExtractor][168][************************************] INFO: Stored entity extraction analysis with ID: 169
2025-08-03 17:07:04,185 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:07:04,185 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:07:04,185 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:07:04,185 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:07:04,185 INFO sqlalchemy.engine.Engine [cached since 54.11s ago] {'analysis_id': 168, 'timestamp': '2025-08-03T17:07:04.120605', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Stored entity extraction analysis with ID: 169", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:07:04.120598", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:07:04,185 - sqlalchemy.engine.Engine - INFO - [cached since 54.11s ago] {'analysis_id': 168, 'timestamp': '2025-08-03T17:07:04.120605', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Stored entity extraction analysis with ID: 169", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:07:04.120598", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:07:04,256 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 17:07:04,256 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 17:07:04,365 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-03 17:07:04,365 - sqlalchemy.engine.Engine - INFO - ROLLBACK
[2025-08-03 17:07:04][EntityExtractor][168][************************************] INFO: Creating response with merged_result keys: ['legal_name', 'business_email', 'support_email', 'business_contact_numbers', 'business_location', 'has_jurisdiction_law', 'jurisdiction_place', 'jurisdiction_details', 'privacy_policy_text', 'terms_conditions_text']
2025-08-03 17:07:04,547 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:07:04,547 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:07:04,548 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:07:04,548 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:07:04,548 INFO sqlalchemy.engine.Engine [cached since 54.47s ago] {'analysis_id': 168, 'timestamp': '2025-08-03T17:07:04.490781', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Creating response with merged_result keys: [\'legal_name\', \'business_email\', \'support_email\', \'business_contact_n ... (133 characters truncated) ...  \'terms_conditions_text\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:07:04.490774", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:07:04,548 - sqlalchemy.engine.Engine - INFO - [cached since 54.47s ago] {'analysis_id': 168, 'timestamp': '2025-08-03T17:07:04.490781', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Creating response with merged_result keys: [\'legal_name\', \'business_email\', \'support_email\', \'business_contact_n ... (133 characters truncated) ...  \'terms_conditions_text\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:07:04.490774", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:07:04,615 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 17:07:04,615 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 17:07:04][EntityExtractor][168][************************************] INFO: Response jurisdiction_details: Indian company, governed by the laws of India
2025-08-03 17:07:04,780 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:07:04,780 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:07:04,781 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:07:04,781 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:07:04,781 INFO sqlalchemy.engine.Engine [cached since 54.7s ago] {'analysis_id': 168, 'timestamp': '2025-08-03T17:07:04.725845', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Response jurisdiction_details: Indian company, governed by the laws of India", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:07:04.725838", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:07:04,781 - sqlalchemy.engine.Engine - INFO - [cached since 54.7s ago] {'analysis_id': 168, 'timestamp': '2025-08-03T17:07:04.725845', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Response jurisdiction_details: Indian company, governed by the laws of India", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:07:04.725838", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:07:04,860 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 17:07:04,860 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 17:07:05][EntityExtractor][168][************************************] INFO: Response jurisdiction_place: ['India']
2025-08-03 17:07:05,063 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:07:05,063 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:07:05,064 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:07:05,064 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:07:05,064 INFO sqlalchemy.engine.Engine [cached since 54.99s ago] {'analysis_id': 168, 'timestamp': '2025-08-03T17:07:05.002074', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Response jurisdiction_place: [\'India\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:07:05.002066", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:07:05,064 - sqlalchemy.engine.Engine - INFO - [cached since 54.99s ago] {'analysis_id': 168, 'timestamp': '2025-08-03T17:07:05.002074', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Response jurisdiction_place: [\'India\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:07:05.002066", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:07:05,116 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 17:07:05,116 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 17:07:05][EntityExtractor][168][************************************] INFO: Response has_jurisdiction_law: yes
2025-08-03 17:07:05,475 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 17:07:05,475 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 17:07:05,475 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:07:05,475 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 17:07:05,475 INFO sqlalchemy.engine.Engine [cached since 55.4s ago] {'analysis_id': 168, 'timestamp': '2025-08-03T17:07:05.389714', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Response has_jurisdiction_law: yes", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:07:05.389707", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:07:05,475 - sqlalchemy.engine.Engine - INFO - [cached since 55.4s ago] {'analysis_id': 168, 'timestamp': '2025-08-03T17:07:05.389714', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Response has_jurisdiction_law: yes", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T17:07:05.389707", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 17:07:05,541 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 17:07:05,541 - sqlalchemy.engine.Engine - INFO - COMMIT
INFO:     127.0.0.1:58472 - "POST /entity-extraction/analyze HTTP/1.1" 200 OK
