"""
Mock module for chatgpt_utils to use in testing
"""
from typing import List, Dict, Any
import json
import re
import time
import traceback
from datetime import datetime

import tiktoken
from openai import AzureOpenAI, OpenAI

from app.config import settings
from app.database import get_session
from app.models.db_models import GeneralLogs as GPTLogs
from app.gpt_models.gpt_logger import api_response_logger

class GPTLogger:
    """Mock GPT Logger class for testing"""
    analysis_id = "test-analysis"
    
    def __init__(self, analysis_id, type="openai"):
        self.analysis_id = analysis_id
        self.type = type
        if type == "openai":
            self.client = OpenAI(api_key=settings.OPENAI_API_KEY)
            self.model = settings.OPENAI_DEPLOYMENT_NAME
        if type == "azure":
            self.client = AzureOpenAI(
                api_key=settings.AZURE_API_KEY,
                api_version=settings.AZURE_API_VERSION,
                azure_endpoint=settings.AZURE_API_BASE,
            )
            self.model = settings.AZURE_DEPLOYMENT_NAME

    @staticmethod
    def get_gpt_4o_response(messages, name=None):
        """Mock method to simulate GPT response"""
        class MockResponse:
            class Choice:
                class Message:
                    content = json.dumps({
                        'home_page': [0],
                        'about_us': [],
                        'terms_and_condition': [],
                        'returns_cancellation_exchange': [],
                        'privacy_policy': [],
                        'shipping_delivery': [],
                        'contact_us': [],
                        'products': [],
                        'services': [],
                        'catalogue': []
                    })
                    
                def __init__(self):
                    self.message = self.Message()
                    
            class Usage:
                def __init__(self):
                    self.prompt_tokens = 100
                    self.completion_tokens = 50
                    
            def __init__(self):
                self.choices = [self.Choice()]
                self.usage = self.Usage()
                
        return MockResponse()

    def get_gpt_4o_response(self, messages, function_name):
        """GPT 4o Model"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=0,
                top_p=0,
                max_tokens=16_000,
                stop=None,
            )
            self.log_gpt_response(
                timestamp,
                function_name,
                json.dumps(messages, indent=4),
                json.dumps(response.choices[0].message.content, indent=4),
                response.usage.prompt_tokens,
                response.usage.completion_tokens,
            )
            return response

        except Exception as e:
            error_type = type(e).__name__
            error_str = str(e)
            print(f"Error getting GPT 4o response, error type: {error_type}, error: {error_str}")
            print(f"Stack trace: {traceback.format_exc()}")

            # Determine if we need to sleep based on error type
            should_sleep = False
            retry_after = 60  # Default retry after 60 seconds

            # Rate limit errors (429)
            if error_type == "RateLimitError" or "429" in error_str:
                should_sleep = True
                # Try to extract retry-after header if available
                if hasattr(e, "response") and hasattr(e.response, "headers"):
                    retry_after_header = e.response.headers.get("retry-after")
                    if retry_after_header and retry_after_header.isdigit():
                        retry_after = int(retry_after_header)
                print(
                    f"Rate limit exceeded. Sleeping for {retry_after} seconds before next request."
                )

            # Service unavailable (503) or server errors (5xx)
            elif (
                error_type == "ServiceUnavailableError"
                or "503" in error_str
                or error_str.startswith("5")
            ):
                should_sleep = True
                retry_after = 30  # Shorter delay for server errors
                print(
                    f"Service unavailable. Sleeping for {retry_after} seconds before next request."
                )

            # Timeout errors
            elif error_type == "APITimeoutError" or "timeout" in error_str.lower():
                should_sleep = True
                retry_after = 20  # Shorter delay for timeouts
                print(f"API timeout. Sleeping for {retry_after} seconds before next request.")

            # Connection errors
            elif error_type == "APIConnectionError" or "connection" in error_str.lower():
                should_sleep = True
                retry_after = 15  # Shorter delay for connection issues
                print(
                    f"API connection error. Sleeping for {retry_after} seconds before next request."
                )

            # Apply sleep if needed
            if should_sleep:
                time.sleep(retry_after)

            self.log_gpt_response(
                timestamp, function_name, json.dumps(messages, indent=4), str(e), 0, 0
            )
            return None

    def gpt4o_completion(self, messages, function_name):
        """Alias for get_gpt_4o_response for backward compatibility"""
        return self.get_gpt_4o_response(messages, function_name)

    def gpt4o_imagefile(self, image_path, prompt, function_name):
        """
        Gpt-4o model
        """
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        if image_path:
            messages = [
                {
                    "role": "system",
                    "content": "You are a helpful assistant to analyse images and extract information and insights.",
                },
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompt},
                        {
                            "type": "image_url",
                            "image_url": {"url": image_path},
                        },
                    ],
                },
            ]
        else:
            print("No image path provided, using text prompt..")
            messages = construct_gpt_messages(
                "You are a helpful assistant to analyse images and extract information and insights.",
                prompt,
            )
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=0,
                top_p=0,
                max_tokens=16_000,
            )
            self.log_gpt_response(
                timestamp,
                function_name,
                json.dumps(messages, indent=4),
                json.dumps(response.choices[0].message.content, indent=4),
                response.usage.prompt_tokens,
                response.usage.completion_tokens,
            )
            return response
        except Exception as e:
            error_type = type(e).__name__
            error_str = str(e)
            print(
                f"Error getting GPT 4o image response, error type: {error_type}, error: {error_str}"
            )
            print(f"Stack trace: {traceback.format_exc()}")

            # Determine if we need to sleep based on error type
            should_sleep = False
            retry_after = 60  # Default retry after 60 seconds

            # Rate limit errors (429)
            if error_type == "RateLimitError" or "429" in error_str:
                should_sleep = True
                # Try to extract retry-after header if available
                if hasattr(e, "response") and hasattr(e.response, "headers"):
                    retry_after_header = e.response.headers.get("retry-after")
                    if retry_after_header and retry_after_header.isdigit():
                        retry_after = int(retry_after_header)
                print(
                    f"Rate limit exceeded. Sleeping for {retry_after} seconds before next request."
                )

            # Service unavailable (503) or server errors (5xx)
            elif (
                error_type == "ServiceUnavailableError"
                or "503" in error_str
                or error_str.startswith("5")
            ):
                should_sleep = True
                retry_after = 30  # Shorter delay for server errors
                print(
                    f"Service unavailable. Sleeping for {retry_after} seconds before next request."
                )

            # Timeout errors
            elif error_type == "APITimeoutError" or "timeout" in error_str.lower():
                should_sleep = True
                retry_after = 20  # Shorter delay for timeouts
                print(f"API timeout. Sleeping for {retry_after} seconds before next request.")

            # Connection errors
            elif error_type == "APIConnectionError" or "connection" in error_str.lower():
                should_sleep = True
                retry_after = 15  # Shorter delay for connection issues
                print(
                    f"API connection error. Sleeping for {retry_after} seconds before next request."
                )

            # Apply sleep if needed
            if should_sleep:
                time.sleep(retry_after)

            self.log_gpt_response(
                timestamp, function_name, json.dumps(messages, indent=4), str(e), 0, 0
            )
            return None

    def log_gpt_response(
        self, timestamp, function_name, messages, response, prompt_tokens, completion_tokens
    ):
        """
        Log GPT response to database with comprehensive error handling
        
        Args:
            timestamp: Timestamp of the response
            function_name: Name of the function that called GPT
            messages: Messages sent to GPT
            response: Response received from GPT
            prompt_tokens: Number of prompt tokens used
            completion_tokens: Number of completion tokens used
        """
        try:
            # Validate inputs with fallbacks
            if not timestamp:
                timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            if not function_name:
                function_name = "unknown_function"
            
            if not messages:
                messages = "[]"
            elif not isinstance(messages, str):
                try:
                    messages = json.dumps(messages, indent=4)
                except Exception:
                    messages = str(messages)
            
            if not response:
                response = "No response"
            elif not isinstance(response, str):
                try:
                    response = json.dumps(response, indent=4)
                except Exception:
                    response = str(response)
            
            # Ensure token counts are valid integers
            try:
                prompt_tokens = int(prompt_tokens) if prompt_tokens is not None else 0
            except (ValueError, TypeError):
                prompt_tokens = 0
                
            try:
                completion_tokens = int(completion_tokens) if completion_tokens is not None else 0
            except (ValueError, TypeError):
                completion_tokens = 0
            
            # Attempt to save to database with proper session management
            try:
                with next(get_session()) as session:
                    # Ensure analysis_id is an integer, default to 0 if invalid
                    try:
                        analysis_id_int = int(self.analysis_id) if self.analysis_id and str(self.analysis_id).isdigit() else 0
                    except (ValueError, TypeError):
                        analysis_id_int = 0

                    log_entry = GPTLogs(
                        analysis_id=analysis_id_int,
                        timestamp=timestamp,
                        type=self.type or "openai",
                        messages=messages,
                        response=response,
                        org_id="default"
                    )

                    session.add(log_entry)
                    session.commit()
                    
            except Exception as db_error:
                # Log to console if database logging fails
                print(f"Failed to log GPT response to database: {str(db_error)}")
                print(f"GPT Log Entry Details:")
                print(f"  Analysis ID: {self.analysis_id}")
                print(f"  Function: {function_name}")
                print(f"  Timestamp: {timestamp}")
                print(f"  Type: {self.type}")
                print(f"  Prompt Tokens: {prompt_tokens}")
                print(f"  Completion Tokens: {completion_tokens}")
                # Don't re-raise, just continue with fallback logging
                
        except Exception as log_error:
            # Final fallback - basic console logging
            print(f"Critical error in log_gpt_response: {str(log_error)}")
            print(f"Function: {function_name}, Analysis ID: {getattr(self, 'analysis_id', 'unknown')}")

# Mock function to call GPT4o mini
def call_gpt4o_mini(messages, logger=None, name=None):
    """
    Mock function to call GPT4o mini with enhanced error handling
    
    Args:
        messages: Messages to send to GPT
        logger: Optional logger instance
        name: Optional name for the function call
        
    Returns:
        GPT response or None if failed
    """
    try:
        # Validate inputs
        if not messages:
            print("Warning: No messages provided to call_gpt4o_mini")
            return None
        
        if not isinstance(messages, list):
            print("Warning: Messages should be a list")
            return None
        
        # Create a GPT logger if one wasn't provided
        if logger is None:
            try:
                logger = GPTLogger("default")
            except Exception as logger_error:
                print(f"Failed to create GPT logger: {str(logger_error)}")
                return None
                
        # Call the model and return the response
        function_name = name or "direct_call"
        response = logger.get_gpt_4o_response(messages, function_name)

        # Also log to the API response logger for consistency
        if response:
            try:
                from app.gpt_models.gpt_logger import api_response_logger
                api_response_logger.log_response(
                    "openai",
                    {"messages": messages, "function_name": function_name},
                    {
                        "response": response.choices[0].message.content if response.choices else None,
                        "usage": {
                            "prompt_tokens": response.usage.prompt_tokens if response.usage else 0,
                            "completion_tokens": response.usage.completion_tokens if response.usage else 0,
                            "total_tokens": response.usage.total_tokens if response.usage else 0
                        }
                    },
                    {"timestamp": datetime.now().isoformat(), "model": logger.model}
                )
            except Exception as log_error:
                print(f"Warning: Failed to log to API response logger: {log_error}")

        return response
        
    except Exception as e:
        print(f"Error in call_gpt4o_mini: {str(e)}")
        print(f"Stack trace: {traceback.format_exc()}")
        return None

# Proper function to count tokens using tiktoken
def num_tokens_from_string(text, encoding_name):
    """
    Count tokens in a string using tiktoken with enhanced error handling
    
    Args:
        text: Text to count tokens for
        encoding_name: Name of the encoding to use
        
    Returns:
        int: Number of tokens
    """
    try:
        # Validate inputs
        if not text or not isinstance(text, str):
            return 0
            
        if not encoding_name:
            encoding_name = "cl100k_base"  # Default for GPT-4
        
        encoding = tiktoken.get_encoding(encoding_name)
        num_tokens = len(encoding.encode(text))
        return num_tokens
        
    except Exception as e:
        print(f"Error counting tokens: {e}")
        # Fallback to word-based estimation with better calculation
        try:
            if text and isinstance(text, str):
                words = text.split()
                # More accurate token estimation: ~1.3 tokens per word + base tokens
                estimated_tokens = int(len(words) * 1.3) + 10
                return max(estimated_tokens, 1)  # Ensure at least 1 token
            else:
                return 1
        except Exception:
            return 1

# Function to clean JSON string
def clean_json_string(json_string):
    """
    Clean JSON string with enhanced error handling
    
    Args:
        json_string: JSON string to clean
        
    Returns:
        str: Cleaned JSON string
    """
    try:
        if not json_string or not isinstance(json_string, str):
            return "{}"
        
        # Remove code block markers
        pattern = r"^```json\s*(.*?)\s*```$"
        cleaned_string_0 = re.sub(pattern, r"\1", json_string, flags=re.DOTALL)

        pattern = r"^```python\s*(.*?)\s*```$"
        cleaned_string = re.sub(pattern, r"\1", cleaned_string_0, flags=re.DOTALL)

        result = cleaned_string.strip()
        
        # Validate that it's at least somewhat JSON-like
        if not result:
            return "{}"
            
        # Basic validation - should start with { or [
        if not (result.startswith('{') or result.startswith('[')):
            return "{}"
            
        return result
        
    except Exception as e:
                        # Silently continue if JSON cleaning fails
        return "{}"

# Function to construct GPT messages
def construct_gpt_messages(system_message, user_message):
    """
    Construct GPT messages with enhanced error handling
    
    Args:
        system_message: System message content
        user_message: User message content
        
    Returns:
        list: List of message dictionaries
    """
    try:
        # Validate inputs with defaults
        if not system_message or not isinstance(system_message, str):
            system_message = "You are a helpful assistant."
            
        if not user_message or not isinstance(user_message, str):
            user_message = "Please help me."
        
        messages = [
            {"role": "system", "content": system_message},
            {"role": "user", "content": user_message},
            {"role": "assistant", "content": ""},
        ]
        return messages
        
    except Exception as e:
        print(f"Error constructing GPT messages: {e}")
        # Return basic fallback messages
        return [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": "Please help me."},
            {"role": "assistant", "content": ""},
        ]
