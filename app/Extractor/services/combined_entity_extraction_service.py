"""
Combined Entity Extraction Service

Service that uses Gemini first, then OpenAI to fill null values
"""

import json
import re
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime

from app.gpt_models.gemini_model_wrapper.gemeni_utils import get_gemini_response_legacy
from app.gpt_models.chatgpt_utils import call_gpt4o_mini
from app.Extractor.utils.logger import EntityExtractorLogger
from app.Extractor.prompts.entity_extraction_prompts import EntityExtractionPrompts
from app.Extractor.services.url_data_retrieval import UrlDataRetrievalService
from app.Extractor.utils.api_response_logger import api_logger


class CombinedEntityExtractionService:
    """
    Service that combines Gemini and OpenAI for comprehensive entity extraction
    """
    
    def __init__(self):
        self.logger = EntityExtractorLogger("combined_entity_extraction")
        self.url_retrieval_service = UrlDataRetrievalService()
        
        # Expected fields for entity extraction
        self.expected_fields = [
            "legal_name",
            "business_email", 
            "support_email",
            "business_contact_numbers",
            "business_location",
            "accepts_international_orders",
            "shipping_countries",
            "shipping_policy_details",
            "has_jurisdiction_law",
            "jurisdiction_place",
            "jurisdiction_details"
        ]
    
    def extract_entities_combined(self, scrape_request_ref_id: str, website_url: str) -> Dict[str, Any]:
        """
        Main method for combined entity extraction
        
        Args:
            scrape_request_ref_id: Reference ID for the scrape request
            website_url: Main website URL
            
        Returns:
            Combined extraction results with metadata
        """
        self.logger.info(f"Starting combined entity extraction for {website_url}")
        
        # Get policy texts from database
        policy_texts = self._get_policy_texts_from_db(scrape_request_ref_id)
        
        if not policy_texts:
            self.logger.warning(f"No policy texts found for scrape_request_ref_id: {scrape_request_ref_id}")
            return {
                "success": False,
                "error": "No policy texts found",
                "gemini_result": {},
                "openai_result": {},
                "combined_result": {},
                "field_sources": {}
            }
        
        # Step 1: Call Gemini first
        gemini_result = self._call_gemini_extraction(policy_texts, website_url, scrape_request_ref_id)
        
        # Step 2: Analyze Gemini results for missing fields
        missing_fields = self._identify_missing_fields(gemini_result)
        
        # Step 3: Call OpenAI for missing fields
        openai_result = {}
        if missing_fields:
            self.logger.info(f"Calling OpenAI for missing fields: {missing_fields}")
            openai_result = self._call_openai_extraction(policy_texts, missing_fields, website_url, scrape_request_ref_id)
        
        # Step 4: Combine results
        combined_result, field_sources = self._merge_results(gemini_result, openai_result)
        
        # Step 5: Generate response
        response = {
            "success": True,
            "website_url": website_url,
            "scrape_request_ref_id": scrape_request_ref_id,
            "timestamp": datetime.now().isoformat(),
            "gemini_result": gemini_result,
            "openai_result": openai_result,
            "combined_result": combined_result,
            "field_sources": field_sources,
            "missing_fields_count": len(missing_fields),
            "total_fields_extracted": len([k for k, v in combined_result.items() if self._is_valid_value(v)])
        }
        
        self.logger.info(f"Combined extraction completed. Total fields: {response['total_fields_extracted']}")
        return response
    
    def _call_gemini_extraction(self, policy_texts: List[Dict], website_url: str, scrape_request_ref_id: str) -> Dict[str, Any]:
        """Call Gemini for entity extraction"""
        try:
            self.logger.info("Calling Gemini for entity extraction")
            
            # Prepare combined text from all policy texts
            combined_text = "\n\n".join([
                f"URL: {text['url']}\nContent: {text['text'][:5000]}"  # Limit text length
                for text in policy_texts
            ])
            
            # Create prompt for Gemini
            prompt = EntityExtractionPrompts.get_entity_extraction_text_prompt(website_url, combined_text)
            
            # Call Gemini
            gemini_response = get_gemini_response_legacy(prompt)
            
            # Log Gemini response
            api_logger.log_gemini_response(
                prompt=prompt,
                response=gemini_response,
                model_name="gemini-2.5-flash",
                request_id=scrape_request_ref_id,
                context={"service": "combined_extraction", "step": "gemini"}
            )
            
            # Parse JSON response
            parsed_result = self._parse_json_response(gemini_response, "Gemini")
            
            self.logger.info(f"Gemini extraction completed. Fields extracted: {len(parsed_result)}")
            return parsed_result
            
        except Exception as e:
            self.logger.error(f"Error in Gemini extraction: {str(e)}")
            return {"error": f"Gemini extraction failed: {str(e)}"}
    
    def _call_openai_extraction(self, policy_texts: List[Dict], missing_fields: List[str], 
                               website_url: str, scrape_request_ref_id: str) -> Dict[str, Any]:
        """Call OpenAI for missing fields"""
        try:
            self.logger.info(f"Calling OpenAI for {len(missing_fields)} missing fields")
            
            # Prepare combined text
            combined_text = "\n\n".join([
                f"URL: {text['url']}\nContent: {text['text'][:5000]}"
                for text in policy_texts
            ])
            
            # Create focused prompt for missing fields only
            prompt = self._create_openai_prompt(website_url, combined_text, missing_fields)
            
            # Call OpenAI
            messages = [{"role": "user", "content": prompt}]
            openai_response_obj = call_gpt4o_mini(messages, name="combined_entity_extraction")
            
            if not openai_response_obj:
                self.logger.warning("No response from OpenAI")
                return {}
            
            openai_response = openai_response_obj.choices[0].message.content
            
            # Log OpenAI response
            api_logger.log_openai_response(
                messages=messages,
                response=openai_response_obj,
                name="combined_entity_extraction",
                request_id=scrape_request_ref_id,
                context={"service": "combined_extraction", "step": "openai", "missing_fields": missing_fields}
            )
            
            # Parse JSON response
            parsed_result = self._parse_json_response(openai_response, "OpenAI")
            
            self.logger.info(f"OpenAI extraction completed. Fields extracted: {len(parsed_result)}")
            return parsed_result
            
        except Exception as e:
            self.logger.error(f"Error in OpenAI extraction: {str(e)}")
            return {"error": f"OpenAI extraction failed: {str(e)}"}
    
    def _identify_missing_fields(self, gemini_result: Dict[str, Any]) -> List[str]:
        """Identify fields that are missing or null in Gemini results"""
        missing_fields = []
        
        for field in self.expected_fields:
            if field not in gemini_result or not self._is_valid_value(gemini_result[field]):
                missing_fields.append(field)
        
        self.logger.info(f"Identified {len(missing_fields)} missing fields: {missing_fields}")
        return missing_fields
    
    def _is_valid_value(self, value: Any) -> bool:
        """Check if a value is valid (not null, empty, or placeholder)"""
        if value is None:
            return False
        
        if isinstance(value, str):
            # Check for various null representations
            null_values = ["", "null", "none", "n/a", "not available", "not found", "unknown"]
            return value.strip().lower() not in null_values
        
        if isinstance(value, list):
            return len(value) > 0 and any(self._is_valid_value(item) for item in value)
        
        return True
    
    def _merge_results(self, gemini_result: Dict[str, Any], openai_result: Dict[str, Any]) -> Tuple[Dict[str, Any], Dict[str, str]]:
        """Merge Gemini and OpenAI results, prioritizing Gemini"""
        combined_result = {}
        field_sources = {}
        
        # Start with all expected fields
        for field in self.expected_fields:
            # Prioritize Gemini result if valid
            if field in gemini_result and self._is_valid_value(gemini_result[field]):
                combined_result[field] = gemini_result[field]
                field_sources[field] = "gemini"
            # Fall back to OpenAI result
            elif field in openai_result and self._is_valid_value(openai_result[field]):
                combined_result[field] = openai_result[field]
                field_sources[field] = "openai"
            # Mark as not found
            else:
                combined_result[field] = ""
                field_sources[field] = "not_found"
        
        return combined_result, field_sources
    
    def _parse_json_response(self, response: str, source: str) -> Dict[str, Any]:
        """Parse JSON response from API"""
        try:
            # Try to extract JSON from response
            json_match = re.search(r"```json\s*(\{.*?\})\s*```", response, re.DOTALL)
            if json_match:
                json_text = json_match.group(1)
            else:
                # Try to find JSON-like content
                json_text = response.strip()
            
            parsed = json.loads(json_text)
            return parsed
            
        except json.JSONDecodeError as e:
            self.logger.warning(f"Failed to parse {source} JSON response: {str(e)}")
            return {"error": f"JSON parsing failed for {source}"}
    
    def _create_openai_prompt(self, website_url: str, text_content: str, missing_fields: List[str]) -> str:
        """Create focused prompt for OpenAI to extract specific missing fields"""
        prompt = f"""
Extract the following specific business information from the website content for {website_url}.

ONLY extract these missing fields: {', '.join(missing_fields)}

Website Content:
{text_content}

Return ONLY a JSON object with the requested fields. Use empty string "" for fields that cannot be found.

Required JSON format:
{{
"""
        
        # Add only the missing fields to the JSON template
        json_fields = []
        for field in missing_fields:
            if field in ["business_contact_numbers", "shipping_countries"]:
                json_fields.append(f'  "{field}": []')
            elif field in ["accepts_international_orders", "has_jurisdiction_law"]:
                json_fields.append(f'  "{field}": false')
            else:
                json_fields.append(f'  "{field}": ""')
        
        prompt += "\n" + ",\n".join(json_fields) + "\n}\n\nReturn only the JSON response, no additional text."
        
        return prompt
    
    def _get_policy_texts_from_db(self, scrape_request_ref_id: str) -> List[Dict[str, str]]:
        """Get policy texts from database"""
        try:
            return self.url_retrieval_service._get_policy_texts_from_db(scrape_request_ref_id)
        except Exception as e:
            self.logger.error(f"Error getting policy texts: {str(e)}")
            return []
